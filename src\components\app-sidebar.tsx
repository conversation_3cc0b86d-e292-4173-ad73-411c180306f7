"use client"

import * as React from "react"
import { useSession } from "next-auth/react"
import { usePathname } from "next/navigation"
import {
  HomeIcon,
  UsersIcon,
  BuildingOfficeIcon,
  UserGroupIcon,
  ChartBarIcon,
  FolderIcon,
  ClockIcon,
  Cog6ToothIcon,
  SparklesIcon,
  ArrowRightIcon,
  RectangleStackIcon,
  ChatBubbleLeftRightIcon,
  CalendarIcon,
  EnvelopeIcon,
  DocumentTextIcon,
} from "@heroicons/react/24/outline"

import { NavMain } from "@/components/nav-main"
import { NavProjects } from "@/components/nav-projects"
import { NavUser } from "@/components/nav-user"
import { TeamSwitcher } from "@/components/team-switcher"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar"
import { usePermissions } from "@/components/providers/permission-provider"
import { PermissionResource, PermissionAction } from "@/types"

export function AppSidebar({ side = "left", ...props }: React.ComponentProps<typeof Sidebar>) {
  const { data: session } = useSession()
  const pathname = usePathname()
  const { hasPermission } = usePermissions()

  // Get user data from session
  const user = session?.user

  // Dynamic user data
  const userData = {
    name: user?.name || "User",
    email: user?.email || "<EMAIL>",
    avatar: user?.avatarUrl || user?.image || "/avatars/default.jpg",
  }

  // Dynamic teams data based on user's tenants
  const teamsData = user?.tenants?.map((tenant, index) => ({
    name: tenant.name,
    logo: index === 0 ? SparklesIcon : ChartBarIcon,
    plan: tenant.role === 'admin' ? 'Enterprise' : 'Professional',
  })) || [
    {
      name: "CRM Platform",
      logo: SparklesIcon,
      plan: "Enterprise",
    },
  ]

  // Dynamic navigation data with permission checks
  const navMainData = [
    {
      title: "Dashboard",
      url: "/dashboard",
      icon: HomeIcon,
      isActive: pathname.startsWith("/dashboard"),
      items: [
        {
          title: "Overview",
          url: "/dashboard",
        },
        {
          title: "Analytics",
          url: "/dashboard/analytics",
        },
        {
          title: "Reports",
          url: "/dashboard/reports",
        },
      ],
    },
    ...(hasPermission(PermissionResource.CONTACTS, PermissionAction.READ) ? [{
      title: "Contacts",
      url: "/contacts",
      icon: UsersIcon,
      isActive: pathname.startsWith("/contacts"),
      items: [
        {
          title: "All Contacts",
          url: "/contacts",
        },
        ...(hasPermission(PermissionResource.CONTACTS, PermissionAction.CREATE) ? [{
          title: "Add Contact",
          url: "/contacts/new",
        }] : []),
        {
          title: "Import Contacts",
          url: "/contacts/import",
        },
      ],
    }] : []),
    ...(hasPermission(PermissionResource.COMPANIES, PermissionAction.READ) ? [{
      title: "Companies",
      url: "/companies",
      icon: BuildingOfficeIcon,
      isActive: pathname.startsWith("/companies"),
      items: [
        {
          title: "All Companies",
          url: "/companies",
        },
        ...(hasPermission(PermissionResource.COMPANIES, PermissionAction.CREATE) ? [{
          title: "Add Company",
          url: "/companies/new",
        }] : []),
      ],
    }] : []),
    ...(hasPermission(PermissionResource.LEADS, PermissionAction.READ) ? [{
      title: "Leads",
      url: "/leads",
      icon: UserGroupIcon,
      isActive: pathname.startsWith("/leads"),
      items: [
        {
          title: "Dashboard",
          url: "/leads/leaddashboard",
        },
        {
          title: "All Leads",
          url: "/leads",
        },
        ...(hasPermission(PermissionResource.LEADS, PermissionAction.CREATE) ? [{
          title: "Add Lead",
          url: "/leads/new",
        }] : []),
        {
          title: "Lead Sources",
          url: "/leads/sources",
        },
        {
          title: "Nurturing Campaigns",
          url: "/leads/nurturing",
        },
        {
          title: "Email Captures",
          url: "/leads/email-capture",
        },
        {
          title: "Social Captures",
          url: "/leads/social-capture",
        },
      ],
    }] : []),
    ...(hasPermission(PermissionResource.OPPORTUNITIES, PermissionAction.READ) ? [{
      title: "Opportunities",
      url: "/opportunities",
      icon: ChartBarIcon,
      isActive: pathname.startsWith("/opportunities"),
      items: [
        {
          title: "All Opportunities",
          url: "/opportunities",
        },
        {
          title: "Pipeline",
          url: "/opportunities/pipeline",
        },
        ...(hasPermission(PermissionResource.OPPORTUNITIES, PermissionAction.CREATE) ? [{
          title: "Create Opportunity",
          url: "/opportunities/new",
        }] : []),
      ],
    }] : []),
    ...(hasPermission(PermissionResource.PROPOSALS, PermissionAction.READ) ? [{
      title: "Proposals",
      url: "/proposals",
      icon: DocumentTextIcon,
      isActive: pathname.startsWith("/proposals"),
      items: [
        {
          title: "All Proposals",
          url: "/proposals",
        },
        {
          title: "Templates",
          url: "/proposals/templates",
        },
        {
          title: "Content Sections",
          url: "/proposals/content-sections",
        },
      ],
    }] : []),
    ...(hasPermission(PermissionResource.OPPORTUNITIES, PermissionAction.READ) ? [{
      title: "Handovers",
      url: "/handovers",
      icon: ArrowRightIcon,
      isActive: pathname.startsWith("/handovers"),
      items: [
        {
          title: "All Handovers",
          url: "/handovers",
        },
        ...(hasPermission(PermissionResource.OPPORTUNITIES, PermissionAction.CREATE) ? [{
          title: "Create Handover",
          url: "/handovers/new",
        }] : []),
        {
          title: "Templates",
          url: "/handovers/templates",
        },
      ],
    }] : []),
    ...(hasPermission(PermissionResource.OPPORTUNITIES, PermissionAction.READ) ? [{
      title: "Projects",
      url: "/projects",
      icon: RectangleStackIcon,
      isActive: pathname.startsWith("/projects"),
      items: [
        {
          title: "Dashboard",
          url: "/projects",
        },
        {
          title: "All Projects",
          url: "/projects/all",
        },
        ...(hasPermission(PermissionResource.OPPORTUNITIES, PermissionAction.CREATE) ? [{
          title: "Create Project",
          url: "/projects/new",
        }] : []),
        {
          title: "Creation Suggestions",
          url: "/projects/creation-suggestions",
        },
      ],
    }] : []),
    ...(hasPermission(PermissionResource.DOCUMENTS, PermissionAction.READ) ? [{
      title: "Documents",
      url: "/documents",
      icon: FolderIcon,
      isActive: pathname.startsWith("/documents"),
      items: [
        {
          title: "All Documents",
          url: "/documents",
        },
        ...(hasPermission(PermissionResource.DOCUMENTS, PermissionAction.CREATE) ? [{
          title: "Upload Documents",
          url: "/documents/upload",
        }] : []),
      ],
    }] : []),
    {
      title: "Communication",
      url: "/communication",
      icon: ChatBubbleLeftRightIcon,
      isActive: pathname.startsWith("/communication"),
      items: [
        {
          title: "Hub",
          url: "/communication",
        },
        {
          title: "Chat",
          url: "/communication?tab=chat",
        },
        {
          title: "Calendar",
          url: "/communication?tab=calendar",
        },
        {
          title: "Templates",
          url: "/communication?tab=templates",
        },
      ],
    },
    {
      title: "Settings",
      url: "/settings",
      icon: Cog6ToothIcon,
      isActive: pathname.startsWith("/settings"),
      items: [
        {
          title: "Profile",
          url: "/settings/profile",
        },
        {
          title: "General",
          url: "/settings",
        },
        {
          title: "Users",
          url: "/settings/users",
        },
        {
          title: "Roles",
          url: "/settings/roles",
        },
        {
          title: "Team",
          url: "/settings/team",
        },
        {
          title: "Integrations",
          url: "/settings/integrations",
        },
        {
          title: "CRM Integration",
          url: "/settings/crm",
        },
        {
          title: "Email Setup",
          url: "/settings/email",
        },
        {
          title: "Social Media",
          url: "/settings/social",
        },
        {
          title: "Notifications",
          url: "/settings/notifications",
        },
        {
          title: "Billing",
          url: "/settings/billing",
        },
      ],
    },
  ]

  // Projects data
  const projectsData = [
    {
      name: "Sales Pipeline",
      url: "/projects/sales-pipeline",
      icon: ChartBarIcon,
    },
    {
      name: "Marketing Campaign",
      url: "/projects/marketing",
      icon: UserGroupIcon,
    },
    {
      name: "Customer Onboarding",
      url: "/projects/onboarding",
      icon: FolderIcon,
    },
    {
      name: "Support Tickets",
      url: "/projects/support",
      icon: ClockIcon,
    },
  ]

  return (
    <Sidebar collapsible="icon" side={side} {...props}>
      <SidebarHeader>
        <TeamSwitcher teams={teamsData} />
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={navMainData} />
        <NavProjects projects={projectsData} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={userData} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}