'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { AlertTriangle, Trash2, Loader2, X, Shield, Users, Building, FileText, Calendar } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { cn } from '@/lib/utils';

export interface RelatedData {
  type: string;
  count: number;
  label: string;
  icon?: React.ReactNode;
  description?: string;
}

export interface DeleteDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => Promise<void>;
  title: string;
  itemName: string;
  itemType: string;
  description?: string;
  relatedData?: RelatedData[];
  requireConfirmation?: boolean;
  confirmationText?: string;
  showImpactWarning?: boolean;
  customWarnings?: string[];
  className?: string;
}

const iconMap = {
  users: Users,
  contacts: Users,
  companies: Building,
  leads: FileText,
  opportunities: Calendar,
  activities: Calendar,
  documents: FileText,
  default: FileText,
};

export function EnhancedDeleteDialog({
  isOpen,
  onClose,
  onConfirm,
  title,
  itemName,
  itemType,
  description,
  relatedData = [],
  requireConfirmation = false,
  confirmationText,
  showImpactWarning = true,
  customWarnings = [],
  className
}: DeleteDialogProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const [confirmationInput, setConfirmationInput] = useState('');
  const [acknowledgeRisks, setAcknowledgeRisks] = useState(false);
  const [countdown, setCountdown] = useState(0);

  const hasRelatedData = relatedData.length > 0;
  const totalRelatedItems = relatedData.reduce((sum, item) => sum + item.count, 0);
  const expectedConfirmationText = confirmationText || itemName;
  const isConfirmationValid = !requireConfirmation || confirmationInput === expectedConfirmationText;
  const canDelete = isConfirmationValid && (!hasRelatedData || acknowledgeRisks) && countdown === 0;

  // Start countdown for dangerous operations
  useEffect(() => {
    if (isOpen && (hasRelatedData || customWarnings.length > 0)) {
      setCountdown(3);
      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
      return () => clearInterval(timer);
    }
  }, [isOpen, hasRelatedData, customWarnings.length]);

  // Reset state when dialog opens/closes
  useEffect(() => {
    if (isOpen) {
      setConfirmationInput('');
      setAcknowledgeRisks(false);
      setIsDeleting(false);
    }
  }, [isOpen]);

  const handleConfirm = async () => {
    if (!canDelete || isDeleting) return;

    try {
      setIsDeleting(true);
      await onConfirm();
      onClose();
    } catch (error) {
      console.error(`Failed to delete ${itemType}:`, error);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleClose = () => {
    if (isDeleting) return;
    onClose();
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape' && !isDeleting) {
      handleClose();
    } else if (e.key === 'Enter' && canDelete && !isDeleting) {
      handleConfirm();
    }
  };

  const getIcon = (type: string) => {
    const IconComponent = iconMap[type as keyof typeof iconMap] || iconMap.default;
    return <IconComponent className="h-4 w-4" />;
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent 
        className={cn(
          "sm:max-w-lg",
          className
        )}
        onKeyDown={handleKeyDown}
        aria-describedby="delete-dialog-description"
      >
        <DialogHeader className="text-center sm:text-left">
          <div className="flex items-center gap-3">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.2, delay: 0.1 }}
              className="flex h-12 w-12 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20"
            >
              <AlertTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
            </motion.div>
            <div className="flex-1">
              <DialogTitle className="text-left text-lg font-semibold text-red-900 dark:text-red-100">
                {title}
              </DialogTitle>
            </div>
          </div>
        </DialogHeader>

        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
          className="space-y-4"
          id="delete-dialog-description"
        >
          <div className="text-sm text-gray-700 dark:text-gray-300">
            {description || (
              <>
                Are you sure you want to delete <span className="font-semibold text-gray-900 dark:text-gray-100">"{itemName}"</span>?
              </>
            )}
          </div>

          {/* Related Data Warning */}
          {hasRelatedData && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              transition={{ duration: 0.3, delay: 0.3 }}
              className="rounded-lg border border-amber-200 bg-amber-50 p-4 dark:border-amber-800 dark:bg-amber-900/20"
            >
              <div className="flex items-start gap-3">
                <AlertTriangle className="h-5 w-5 text-amber-600 dark:text-amber-400 mt-0.5 flex-shrink-0" />
                <div className="flex-1">
                  <p className="font-medium text-amber-800 dark:text-amber-200 mb-2">
                    This will also delete {totalRelatedItems} related item{totalRelatedItems !== 1 ? 's' : ''}:
                  </p>
                  <ul className="space-y-2">
                    {relatedData.map((item, index) => (
                      <li key={index} className="flex items-center gap-2 text-sm text-amber-700 dark:text-amber-300">
                        {getIcon(item.type)}
                        <span className="font-medium">{item.count}</span>
                        <span>{item.label}</span>
                        {item.description && (
                          <span className="text-amber-600 dark:text-amber-400">({item.description})</span>
                        )}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </motion.div>
          )}

          {/* Custom Warnings */}
          {customWarnings.length > 0 && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              transition={{ duration: 0.3, delay: 0.4 }}
              className="space-y-2"
            >
              {customWarnings.map((warning, index) => (
                <div key={index} className="rounded-lg border border-orange-200 bg-orange-50 p-3 dark:border-orange-800 dark:bg-orange-900/20">
                  <div className="flex items-start gap-2">
                    <Shield className="h-4 w-4 text-orange-600 dark:text-orange-400 mt-0.5 flex-shrink-0" />
                    <p className="text-sm text-orange-800 dark:text-orange-200">{warning}</p>
                  </div>
                </div>
              ))}
            </motion.div>
          )}

          {/* Permanent Warning */}
          {showImpactWarning && (
            <div className="rounded-lg border border-red-200 bg-red-50 p-4 dark:border-red-800 dark:bg-red-900/20">
              <div className="flex items-start gap-3">
                <Trash2 className="h-5 w-5 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-sm text-red-800 dark:text-red-200">
                    <span className="font-medium">Warning:</span> This action is permanent and cannot be reversed. 
                    All data will be permanently deleted from the system.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Risk Acknowledgment */}
          {hasRelatedData && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3, delay: 0.5 }}
              className="flex items-start gap-3 p-3 rounded-lg bg-gray-50 dark:bg-gray-800/50"
            >
              <Checkbox
                id="acknowledge-risks"
                checked={acknowledgeRisks}
                onCheckedChange={setAcknowledgeRisks}
                className="mt-0.5"
              />
              <label htmlFor="acknowledge-risks" className="text-sm text-gray-700 dark:text-gray-300 cursor-pointer">
                I understand that this will permanently delete all related data and cannot be undone.
              </label>
            </motion.div>
          )}

          {/* Confirmation Input */}
          {requireConfirmation && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3, delay: 0.6 }}
              className="space-y-2"
            >
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Type <span className="font-mono bg-gray-100 dark:bg-gray-800 px-1 rounded">{expectedConfirmationText}</span> to confirm:
              </label>
              <Input
                value={confirmationInput}
                onChange={(e) => setConfirmationInput(e.target.value)}
                placeholder={expectedConfirmationText}
                className="font-mono"
                disabled={isDeleting}
              />
            </motion.div>
          )}
        </motion.div>

        <DialogFooter className="gap-2 sm:gap-0">
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isDeleting}
            className="sm:mr-2"
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleConfirm}
            disabled={!canDelete || isDeleting}
            className="gap-2"
          >
            {isDeleting ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                Deleting...
              </>
            ) : countdown > 0 ? (
              <>
                <Trash2 className="h-4 w-4" />
                Delete in {countdown}s
              </>
            ) : (
              <>
                <Trash2 className="h-4 w-4" />
                Delete {itemType}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

// Hook for easier usage
export function useEnhancedDeleteDialog() {
  const [dialog, setDialog] = useState<{
    isOpen: boolean;
    props: Partial<DeleteDialogProps>;
  }>({
    isOpen: false,
    props: {},
  });

  const showDeleteDialog = (props: Omit<DeleteDialogProps, 'isOpen' | 'onClose'>) => {
    return new Promise<boolean>((resolve) => {
      setDialog({
        isOpen: true,
        props: {
          ...props,
          onConfirm: async () => {
            try {
              await props.onConfirm();
              resolve(true);
              setDialog(prev => ({ ...prev, isOpen: false }));
            } catch (error) {
              console.error('Delete action failed:', error);
              resolve(false);
            }
          },
          onClose: () => {
            resolve(false);
            setDialog(prev => ({ ...prev, isOpen: false }));
          },
        },
      });
    });
  };

  const closeDialog = () => {
    setDialog({
      isOpen: false,
      props: {},
    });
  };

  const DialogComponent = () => (
    <EnhancedDeleteDialog
      isOpen={dialog.isOpen}
      onClose={dialog.props.onClose || (() => {})}
      onConfirm={dialog.props.onConfirm || (() => Promise.resolve())}
      title={dialog.props.title || 'Delete Item'}
      itemName={dialog.props.itemName || ''}
      itemType={dialog.props.itemType || 'item'}
      {...dialog.props}
    />
  );

  return {
    showDeleteDialog,
    closeDialog,
    DialogComponent,
  };
}

// Utility functions for common delete scenarios
export const deleteConfigurations = {
  contact: (contactName: string, relatedData?: RelatedData[]) => ({
    title: 'Delete Contact',
    itemType: 'Contact',
    itemName: contactName,
    description: `Are you sure you want to delete the contact "${contactName}"?`,
    relatedData,
    requireConfirmation: relatedData && relatedData.length > 0,
    confirmationText: relatedData && relatedData.length > 0 ? contactName : undefined,
    customWarnings: [
      'This contact will be removed from all associated companies and activities.',
      'Any scheduled meetings or tasks related to this contact will remain but lose the contact association.',
    ],
  }),

  company: (companyName: string, relatedData?: RelatedData[]) => ({
    title: 'Delete Company',
    itemType: 'Company',
    itemName: companyName,
    description: `Are you sure you want to delete the company "${companyName}"?`,
    relatedData,
    requireConfirmation: relatedData && relatedData.length > 0,
    confirmationText: relatedData && relatedData.length > 0 ? companyName : undefined,
    customWarnings: [
      'All associated contacts will lose their company association.',
      'Company-specific activities and opportunities will be permanently deleted.',
    ],
  }),

  activity: (activityTitle: string) => ({
    title: 'Delete Activity',
    itemType: 'Activity',
    itemName: activityTitle,
    description: `Are you sure you want to delete the activity "${activityTitle}"?`,
    customWarnings: [
      'This activity will be permanently removed from all timelines.',
      'Any associated notes or attachments will also be deleted.',
    ],
  }),

  lead: {
    title: 'Delete Lead',
    itemType: 'Lead',
    description: 'Are you sure you want to delete this lead?',
    customWarnings: [
      'This lead will be permanently removed from your CRM.',
      'All associated activities and AI insights will be deleted.',
      'Any opportunities created from this lead will remain but lose the lead association.',
    ],
  },

  bulkLeads: {
    title: 'Delete Multiple Leads',
    itemType: 'Leads',
    description: 'Are you sure you want to delete the selected leads?',
    requireConfirmation: true,
    customWarnings: [
      'This will permanently delete all selected leads and their associated data.',
      'All activities and AI insights for these leads will be removed.',
      'This action affects multiple records and cannot be undone.',
    ],
  },

  opportunity: {
    title: 'Delete Opportunity',
    itemType: 'Opportunity',
    description: 'Are you sure you want to delete this opportunity?',
    customWarnings: [
      'This opportunity will be permanently removed from your CRM.',
      'All associated activities, proposals, and stage history will be deleted.',
      'Any related documents and AI insights will be removed.',
      'Revenue forecasts and pipeline reports will be updated.',
    ],
  },

  bulkOpportunities: {
    title: 'Delete Multiple Opportunities',
    itemType: 'Opportunities',
    description: 'Are you sure you want to delete the selected opportunities?',
    requireConfirmation: true,
    customWarnings: [
      'This will permanently delete all selected opportunities and their associated data.',
      'All activities, proposals, and stage history for these opportunities will be removed.',
      'Revenue forecasts and pipeline reports will be significantly affected.',
      'This action affects multiple records and cannot be undone.',
    ],
  },

  handover: {
    title: 'Delete Handover',
    itemType: 'Handover',
    description: 'Are you sure you want to delete this handover?',
    customWarnings: [
      'This handover will be permanently removed from your system.',
      'All checklist items and progress will be permanently lost.',
      'All handover documents and Q&A threads will be deleted.',
      'Any notifications related to this handover will be removed.',
      'This action cannot be undone.',
    ],
  },

  document: (documentName: string) => ({
    title: 'Delete Document',
    itemType: 'Document',
    itemName: documentName,
    description: `Are you sure you want to delete the document "${documentName}"?`,
    customWarnings: [
      'The document file will be permanently removed from storage.',
      'This action cannot be undone and the file cannot be recovered.',
      'Any references to this document will become invalid.',
    ],
  }),

  proposal: (proposalName: string) => ({
    title: 'Delete Proposal',
    itemType: 'Proposal',
    itemName: proposalName,
    description: `Are you sure you want to delete the proposal "${proposalName}"?`,
    customWarnings: [
      'This proposal will be permanently removed from your CRM.',
      'All associated sections, charts, and AI-generated content will be deleted.',
      'Any generated documents and files will be removed.',
      'This action cannot be undone.',
    ],
  }),

  project: (projectName: string) => ({
    title: 'Delete Project',
    itemType: 'Project',
    itemName: projectName,
    description: `Are you sure you want to delete the project "${projectName}"?`,
    customWarnings: [
      'This project will be permanently removed from your system.',
      'All associated tasks, milestones, and progress will be deleted.',
      'Any project documents and files will be removed.',
      'Time tracking data and resource allocations will be lost.',
      'This action cannot be undone.',
    ],
  }),

  bulkDocuments: {
    title: 'Delete Multiple Documents',
    itemType: 'Documents',
    description: 'Are you sure you want to delete the selected documents?',
    requireConfirmation: true,
    customWarnings: [
      'This will permanently delete all selected documents and their files from storage.',
      'All document files will be removed and cannot be recovered.',
      'Any references to these documents will become invalid.',
      'This action affects multiple files and cannot be undone.',
    ],
  },

  bulkDelete: (itemType: string, count: number, relatedData?: RelatedData[]) => ({
    title: `Delete ${count} ${itemType}s`,
    itemType: itemType,
    itemName: `${count} ${itemType.toLowerCase()}s`,
    description: `Are you sure you want to delete ${count} ${itemType.toLowerCase()}s?`,
    relatedData,
    requireConfirmation: true,
    confirmationText: `DELETE ${count} ${itemType.toUpperCase()}S`,
    customWarnings: [
      `This will permanently delete ${count} ${itemType.toLowerCase()}s and all their associated data.`,
      'This action affects multiple records and cannot be undone.',
    ],
  }),
};

// Quick delete functions
export const confirmContactDelete = (
  contactName: string,
  onConfirm: () => Promise<void>,
  relatedData?: RelatedData[]
) => {
  const { showDeleteDialog, DialogComponent } = useEnhancedDeleteDialog();
  return {
    confirm: () => showDeleteDialog({
      ...deleteConfigurations.contact(contactName, relatedData),
      onConfirm,
    }),
    DialogComponent,
  };
};

export const confirmCompanyDelete = (
  companyName: string,
  onConfirm: () => Promise<void>,
  relatedData?: RelatedData[]
) => {
  const { showDeleteDialog, DialogComponent } = useEnhancedDeleteDialog();
  return {
    confirm: () => showDeleteDialog({
      ...deleteConfigurations.company(companyName, relatedData),
      onConfirm,
    }),
    DialogComponent,
  };
};

export const confirmDocumentDelete = (
  documentName: string,
  onConfirm: () => Promise<void>
) => {
  const { showDeleteDialog, DialogComponent } = useEnhancedDeleteDialog();
  return {
    confirm: () => showDeleteDialog({
      ...deleteConfigurations.document(documentName),
      onConfirm,
    }),
    DialogComponent,
  };
};
