'use client';

import React from 'react';
import { useTranslation, useLocale } from '@/components/providers/locale-provider';
import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { 
  HomeIcon,
  UsersIcon,
  BuildingOfficeIcon,
  UserGroupIcon,
  ChartBarIcon,
  DocumentTextIcon,
  FolderIcon,
  ClockIcon,
  Cog6ToothIcon,
  ShieldCheckIcon,
  CurrencyDollarIcon,
  SparklesIcon,
  ChevronLeftIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline';
import { cn } from '@/lib/utils';
import { useUIStore } from '@/stores/ui-store';

const navigationItems = [
  {
    name: 'dashboard',
    href: '/dashboard',
    icon: HomeIcon,
  },
  {
    name: 'contacts',
    href: '/contacts',
    icon: UsersIcon,
  },
  {
    name: 'companies',
    href: '/companies',
    icon: BuildingOfficeIcon,
  },
  {
    name: 'leads',
    href: '/leads',
    icon: UserGroupIcon,
  },
  {
    name: 'opportunities',
    href: '/opportunities',
    icon: ChartBarIcon,
  },
  {
    name: 'proposals',
    href: '/proposals',
    icon: DocumentTextIcon,
  },
  {
    name: 'dashboard',
    href: '/projects',
    icon: FolderIcon,
    current: false,
  },
  {
    name: 'projects',
    href: '/projects/all',
    icon: FolderIcon,
    current: false,
  },
  {
    name: 'activities',
    href: '/activities',
    icon: ClockIcon,
  },
];

const adminItems = [
  {
    name: 'users',
    href: '/admin/users',
    icon: UsersIcon,
  },
  {
    name: 'roles',
    href: '/admin/roles',
    icon: ShieldCheckIcon,
  },
  {
    name: 'billing',
    href: '/admin/billing',
    icon: CurrencyDollarIcon,
  },
  {
    name: 'settings',
    href: '/admin/settings',
    icon: Cog6ToothIcon,
  },
];

export function CollapsibleSidebar() {
  const { t } = useTranslation();
  const { isRTL } = useLocale();
  const pathname = usePathname();
  const { sidebarCollapsed, toggleSidebar } = useUIStore();

  const CollapseIcon = isRTL 
    ? (sidebarCollapsed ? ChevronLeftIcon : ChevronRightIcon)
    : (sidebarCollapsed ? ChevronRightIcon : ChevronLeftIcon);

  // Calculate sidebar positioning for RTL/LTR
  const getSidebarClasses = () => {
    const baseClasses = "hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:flex-col transition-all duration-300 ease-in-out";
    const widthClasses = sidebarCollapsed ? "lg:w-16" : "lg:w-64";

    if (isRTL) {
      // For RTL, position sidebar on the right
      return `${baseClasses} ${widthClasses} lg:right-0`;
    } else {
      // For LTR, position sidebar on the left (default)
      return `${baseClasses} ${widthClasses} lg:left-0`;
    }
  };

  const getBorderClasses = () => {
    if (isRTL) {
      return "border-l border-gray-200 dark:border-gray-800"; // Left border for RTL
    } else {
      return "border-r border-gray-200 dark:border-gray-800"; // Right border for LTR
    }
  };

  return (
    <div className={getSidebarClasses()}>
      <div className={cn(
        "flex grow flex-col gap-y-5 overflow-y-auto bg-card border-r border-border shadow-sm",
        getBorderClasses()
      )}>
        {/* Header */}
        <div className={cn(
          "flex h-16 shrink-0 items-center justify-between px-4",
          sidebarCollapsed && "px-2"
        )}>
          {!sidebarCollapsed && (
            <div className={cn(
              "flex items-center",
              isRTL ? "space-x-reverse space-x-3" : "space-x-3"
            )}>
              <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary shadow-sm">
                <SparklesIcon className="h-5 w-5 text-primary-foreground" />
              </div>
              <div className="min-w-0">
                <h1 className="text-lg font-bold text-card-foreground truncate">
                  CRM Platform
                </h1>
                <p className="text-xs text-muted-foreground truncate">
                  Multi-Tenant SaaS
                </p>
              </div>
            </div>
          )}

          {sidebarCollapsed && (
            <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary shadow-sm mx-auto">
              <SparklesIcon className="h-5 w-5 text-primary-foreground" />
            </div>
          )}

          <button
            onClick={toggleSidebar}
            className={cn(
              "flex h-8 w-8 items-center justify-center rounded-md text-muted-foreground hover:bg-accent hover:text-accent-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 transition-colors",
              sidebarCollapsed && "mx-auto mt-2"
            )}
            title={sidebarCollapsed ? String(t('sidebar.expand')) : String(t('sidebar.collapse'))}
          >
            <CollapseIcon className="h-4 w-4" />
          </button>
        </div>

        {/* Navigation */}
        <nav className="flex flex-1 flex-col px-4 pb-4">
          <ul role="list" className="flex flex-1 flex-col gap-y-7">
            {/* Main Navigation */}
            <li>
              {!sidebarCollapsed && (
                <div className="text-xs font-semibold leading-6 text-muted-foreground uppercase tracking-wide mb-2">
                  {t('navigation.main')}
                </div>
              )}
              <ul role="list" className="-mx-2 space-y-1">
                {navigationItems.map((item) => {
                  const isActive = pathname === item.href || pathname.startsWith(item.href + '/');
                  return (
                    <li key={item.name}>
                      <Link
                        href={item.href}
                        className={cn(
                          "group flex rounded-md p-2 text-sm leading-6 font-medium transition-all duration-200",
                          isRTL ? "gap-x-reverse gap-x-3" : "gap-x-3",
                          isActive
                            ? 'bg-accent text-accent-foreground shadow-sm'
                            : 'text-muted-foreground hover:text-foreground hover:bg-accent',
                          sidebarCollapsed && "justify-center"
                        )}
                        title={sidebarCollapsed ? String(t(`navigation.${item.name}`)) : undefined}
                      >
                        <item.icon
                          className={cn(
                            "h-6 w-6 shrink-0 transition-colors",
                            isActive
                              ? "text-foreground"
                              : "text-muted-foreground group-hover:text-foreground"
                          )}
                        />
                        {!sidebarCollapsed && (
                          <span className="truncate">{t(`navigation.${item.name}`)}</span>
                        )}
                      </Link>
                    </li>
                  );
                })}
              </ul>
            </li>

            {/* Admin Section */}
            <li>
              {!sidebarCollapsed && (
                <div className="text-xs font-semibold leading-6 text-muted-foreground uppercase tracking-wide mb-2">
                  {t('navigation.admin')}
                </div>
              )}
              <ul role="list" className="-mx-2 space-y-1">
                {adminItems.map((item) => {
                  const isActive = pathname === item.href || pathname.startsWith(item.href + '/');
                  return (
                    <li key={item.name}>
                      <Link
                        href={item.href}
                        className={cn(
                          "group flex rounded-md p-2 text-sm leading-6 font-medium transition-all duration-200",
                          isRTL ? "gap-x-reverse gap-x-3" : "gap-x-3",
                          isActive
                            ? 'bg-accent text-accent-foreground shadow-sm'
                            : 'text-muted-foreground hover:text-foreground hover:bg-accent',
                          sidebarCollapsed && "justify-center"
                        )}
                        title={sidebarCollapsed ? String(t(`navigation.${item.name}`)) : undefined}
                      >
                        <item.icon
                          className={cn(
                            "h-6 w-6 shrink-0 transition-colors",
                            isActive
                              ? "text-foreground"
                              : "text-muted-foreground group-hover:text-foreground"
                          )}
                        />
                        {!sidebarCollapsed && (
                          <span className="truncate">{t(`navigation.${item.name}`)}</span>
                        )}
                      </Link>
                    </li>
                  );
                })}
              </ul>
            </li>
          </ul>
        </nav>
      </div>
    </div>
  );
}
