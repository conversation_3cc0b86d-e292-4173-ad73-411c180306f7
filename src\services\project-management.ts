import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { Decimal } from '@prisma/client/runtime/library';
import { UnifiedNotificationService } from './unified-notification-service';

// Helper function to parse date values (supports Date objects, strings in YYYY-MM-DD and ISO datetime formats)
const dateSchema = z.union([
  z.string(),
  z.date()
]).optional().transform((val, ctx) => {
  if (!val) return undefined;

  // If it's already a Date object, return it
  if (val instanceof Date) {
    if (isNaN(val.getTime())) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Invalid date object',
      });
      return z.NEVER;
    }
    return val;
  }

  // Handle string values
  if (typeof val === 'string') {
    // Handle YYYY-MM-DD format (from HTML date inputs)
    if (/^\d{4}-\d{2}-\d{2}$/.test(val)) {
      const date = new Date(val + 'T00:00:00.000Z');
      if (isNaN(date.getTime())) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Invalid date',
        });
        return z.NEVER;
      }
      return date;
    }

    // Handle ISO datetime format
    if (/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$/.test(val)) {
      const date = new Date(val);
      if (isNaN(date.getTime())) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Invalid datetime',
        });
        return z.NEVER;
      }
      return date;
    }

    // Try to parse any other string format
    const date = new Date(val);
    if (isNaN(date.getTime())) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Invalid date format. Expected YYYY-MM-DD, ISO datetime, or Date object',
      });
      return z.NEVER;
    }
    return date;
  }

  // Invalid type
  ctx.addIssue({
    code: z.ZodIssueCode.custom,
    message: 'Expected string or Date object',
  });
  return z.NEVER;
});

// Validation schemas
export const createProjectSchema = z.object({
  name: z.string().min(1, 'Project name is required').max(255),
  description: z.string().optional(),
  status: z.enum(['planning', 'in_progress', 'on_hold', 'completed', 'cancelled']).default('planning'),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).default('medium'),
  startDate: dateSchema,
  endDate: dateSchema,
  budget: z.number().positive().optional(),
  managerId: z.string().optional(),
  estimatedHours: z.number().positive().optional(),
  clientPortalEnabled: z.boolean().default(false),
  integrationProvider: z.string().optional(),
  integrationConfig: z.record(z.any()).optional(),
  opportunityId: z.string().optional(),
});

export const updateProjectSchema = createProjectSchema.partial();

export const createTaskSchema = z.object({
  title: z.string().min(1, 'Task title is required').max(255),
  description: z.string().optional(),
  status: z.enum(['todo', 'in_progress', 'review', 'done']).default('todo'),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).default('medium'),
  assigneeId: z.string().optional(),
  startDate: dateSchema,
  dueDate: dateSchema,
  estimatedHours: z.number().positive().optional(),
  parentTaskId: z.string().optional(),
  dependsOnTasks: z.array(z.string()).default([]),
});

export const updateTaskSchema = createTaskSchema.partial();

export const createMilestoneSchema = z.object({
  title: z.string().min(1, 'Milestone title is required').max(255),
  description: z.string().optional(),
  dueDate: dateSchema.refine(val => val !== undefined, { message: 'Due date is required' }),
  dependsOnTasks: z.array(z.string()).default([]),
});

export const updateMilestoneSchema = createMilestoneSchema.partial();

export const createResourceSchema = z.object({
  userId: z.string(),
  role: z.string().min(1, 'Role is required'),
  allocation: z.number().min(0).max(100).default(100),
  hourlyRate: z.number().positive().optional(),
  startDate: dateSchema,
  endDate: dateSchema.optional(),
  hoursAllocated: z.number().positive().optional(),
});

export const updateResourceSchema = createResourceSchema.partial();

// Types
export type CreateProjectData = z.infer<typeof createProjectSchema>;
export type UpdateProjectData = z.infer<typeof updateProjectSchema>;
export type CreateTaskData = z.infer<typeof createTaskSchema>;
export type UpdateTaskData = z.infer<typeof updateTaskSchema>;
export type CreateMilestoneData = z.infer<typeof createMilestoneSchema>;
export type UpdateMilestoneData = z.infer<typeof updateMilestoneSchema>;
export type CreateResourceData = z.infer<typeof createResourceSchema>;
export type UpdateResourceData = z.infer<typeof updateResourceSchema>;

export interface ProjectWithDetails {
  id: string;
  tenantId: string;
  opportunityId?: string;
  handoverId?: string;
  name: string;
  description?: string;
  status: string;
  priority: string;
  startDate?: Date;
  endDate?: Date;
  budget?: Decimal;
  managerId?: string;
  progress: Decimal;
  estimatedHours?: number;
  actualHours?: number;
  clientPortalEnabled: boolean;
  clientPortalUrl?: string;
  integrationProvider?: string;
  lastSyncAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  
  // Relations
  manager?: {
    id: string;
    firstName?: string;
    lastName?: string;
    email: string;
  };
  opportunity?: {
    id: string;
    title: string;
    value?: Decimal;
  };
  tasks?: Array<{
    id: string;
    title: string;
    status: string;
    priority: string;
    assigneeId?: string;
    dueDate?: Date;
    estimatedHours?: Decimal;
    actualHours?: Decimal;
  }>;
  milestones?: Array<{
    id: string;
    title: string;
    status: string;
    dueDate: Date;
    progress: Decimal;
  }>;
  resources?: Array<{
    id: string;
    userId: string;
    role: string;
    allocation: Decimal;
    hoursAllocated?: Decimal;
    hoursLogged: Decimal;
    user: {
      id: string;
      firstName?: string;
      lastName?: string;
      email: string;
    };
  }>;
}

export class ProjectManagementService {
  private notificationService = new UnifiedNotificationService();

  private readonly includeOptions = {
    manager: {
      select: {
        id: true,
        firstName: true,
        lastName: true,
        email: true,
      },
    },
    opportunity: {
      select: {
        id: true,
        title: true,
        value: true,
      },
    },
    tasks: {
      select: {
        id: true,
        title: true,
        status: true,
        priority: true,
        assigneeId: true,
        dueDate: true,
        estimatedHours: true,
        actualHours: true,
      },
      orderBy: {
        createdAt: 'desc' as const,
      },
      take: 10, // Limit to recent tasks
    },
    milestones: {
      select: {
        id: true,
        title: true,
        status: true,
        dueDate: true,
        progress: true,
      },
      orderBy: {
        dueDate: 'asc' as const,
      },
    },
    resources: {
      select: {
        id: true,
        userId: true,
        role: true,
        allocation: true,
        hoursAllocated: true,
        hoursLogged: true,
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
      where: {
        isActive: true,
      },
    },
  };

  /**
   * Get all projects for a tenant with pagination and filtering
   */
  async getProjects(
    tenantId: string,
    options: {
      page?: number;
      limit?: number;
      status?: string;
      priority?: string;
      managerId?: string;
      opportunityId?: string;
      search?: string;
      includeDetails?: boolean;
    } = {}
  ): Promise<{
    projects: ProjectWithDetails[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const {
      page = 1,
      limit = 10,
      status,
      priority,
      managerId,
      opportunityId,
      search,
      includeDetails = false,
    } = options;

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {
      tenantId,
    };

    if (status) {
      where.status = status;
    }

    if (priority) {
      where.priority = priority;
    }

    if (managerId) {
      where.managerId = managerId;
    }

    if (opportunityId) {
      where.opportunityId = opportunityId;
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }

    // Get total count
    const total = await prisma.project.count({ where });

    // Get projects
    const projects = await prisma.project.findMany({
      where,
      include: includeDetails ? this.includeOptions : {
        manager: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        _count: {
          select: {
            tasks: true,
            milestones: true,
          },
        },
      },
      orderBy: {
        updatedAt: 'desc',
      },
      skip,
      take: limit,
    });

    const totalPages = Math.ceil(total / limit);

    return {
      projects: projects as ProjectWithDetails[],
      total,
      page,
      limit,
      totalPages,
    };
  }

  /**
   * Get a single project by ID
   */
  async getProjectById(
    projectId: string,
    tenantId: string,
    includeDetails: boolean = true
  ): Promise<ProjectWithDetails | null> {
    const project = await prisma.project.findFirst({
      where: {
        id: projectId,
        tenantId,
      },
      include: includeDetails ? this.includeOptions : undefined,
    });

    return project as ProjectWithDetails | null;
  }

  /**
   * Create a new project
   */
  async createProject(
    tenantId: string,
    data: CreateProjectData,
    createdBy: string
  ): Promise<ProjectWithDetails> {
    const validatedData = createProjectSchema.parse(data);

    return await prisma.$transaction(async (tx) => {
      // Verify manager belongs to tenant if provided
      if (validatedData.managerId) {
        const manager = await tx.user.findFirst({
          where: {
            id: validatedData.managerId,
            tenantUsers: {
              some: { tenantId }
            }
          }
        });

        if (!manager) {
          throw new Error('Project manager not found or not part of tenant');
        }
      }

      // Verify opportunity belongs to tenant if provided
      if (validatedData.opportunityId) {
        const opportunity = await tx.opportunity.findFirst({
          where: {
            id: validatedData.opportunityId,
            tenantId
          }
        });

        if (!opportunity) {
          throw new Error('Opportunity not found or not part of tenant');
        }

        // Check if project already exists for this opportunity
        const existingProject = await tx.project.findFirst({
          where: {
            opportunityId: validatedData.opportunityId,
            tenantId
          }
        });

        if (existingProject) {
          throw new Error(`A project "${existingProject.name}" already exists for this opportunity. You can view it at /projects/${existingProject.id}`);
        }
      }

      // Create the project
      const project = await tx.project.create({
        data: {
          tenantId,
          opportunityId: validatedData.opportunityId,
          name: validatedData.name,
          description: validatedData.description,
          status: validatedData.status,
          priority: validatedData.priority,
          startDate: validatedData.startDate,
          endDate: validatedData.endDate,
          budget: validatedData.budget,
          managerId: validatedData.managerId,
          estimatedHours: validatedData.estimatedHours,
          clientPortalEnabled: validatedData.clientPortalEnabled,
          integrationProvider: validatedData.integrationProvider,
          integrationConfig: validatedData.integrationConfig,
          createdById: createdBy,
        },
        include: this.includeOptions,
      });

      // Trigger project creation notification
      await this.triggerProjectCreationNotification(project as ProjectWithDetails, tenantId);

      return project as ProjectWithDetails;
    });
  }

  /**
   * Update a project
   */
  async updateProject(
    projectId: string,
    tenantId: string,
    data: UpdateProjectData,
    updatedBy: string
  ): Promise<ProjectWithDetails> {
    const validatedData = updateProjectSchema.parse(data);

    return await prisma.$transaction(async (tx) => {
      // Verify project exists and belongs to tenant
      const existingProject = await tx.project.findFirst({
        where: {
          id: projectId,
          tenantId,
        },
      });

      if (!existingProject) {
        throw new Error('Project not found');
      }

      // Verify manager belongs to tenant if provided
      if (validatedData.managerId) {
        const manager = await tx.user.findFirst({
          where: {
            id: validatedData.managerId,
            tenantUsers: {
              some: { tenantId }
            }
          }
        });

        if (!manager) {
          throw new Error('Project manager not found or not part of tenant');
        }
      }

      // Verify opportunity belongs to tenant if provided
      if (validatedData.opportunityId) {
        const opportunity = await tx.opportunity.findFirst({
          where: {
            id: validatedData.opportunityId,
            tenantId
          }
        });

        if (!opportunity) {
          throw new Error('Opportunity not found or not part of tenant');
        }
      }

      // Update the project
      const project = await tx.project.update({
        where: {
          id: projectId,
        },
        data: {
          ...validatedData,
          updatedAt: new Date(),
        },
        include: this.includeOptions,
      });

      // Trigger project update notifications
      await this.handleProjectUpdateNotifications(
        project as ProjectWithDetails,
        existingProject,
        tenantId
      );

      return project as ProjectWithDetails;
    });
  }

  /**
   * Delete a project
   */
  async deleteProject(projectId: string, tenantId: string): Promise<void> {
    await prisma.$transaction(async (tx) => {
      // Verify project exists and belongs to tenant
      const project = await tx.project.findFirst({
        where: {
          id: projectId,
          tenantId,
        },
      });

      if (!project) {
        throw new Error('Project not found');
      }

      // Delete the project (cascade will handle related records)
      await tx.project.delete({
        where: {
          id: projectId,
        },
      });
    });
  }

  /**
   * Get project statistics for a tenant
   */
  async getProjectStats(tenantId: string): Promise<{
    total: number;
    byStatus: Record<string, number>;
    totalBudget: number;
    totalEstimatedHours: number;
    totalActualHours: number;
    averageProgress: number;
  }> {
    const projects = await prisma.project.findMany({
      where: { tenantId },
      select: {
        status: true,
        budget: true,
        estimatedHours: true,
        actualHours: true,
        progress: true,
      },
    });

    const stats = {
      total: projects.length,
      byStatus: {} as Record<string, number>,
      totalBudget: 0,
      totalEstimatedHours: 0,
      totalActualHours: 0,
      averageProgress: 0,
    };

    let totalProgress = 0;

    projects.forEach((project) => {
      // Count by status
      stats.byStatus[project.status] = (stats.byStatus[project.status] || 0) + 1;

      // Sum budgets and hours
      if (project.budget) {
        stats.totalBudget += Number(project.budget);
      }
      if (project.estimatedHours) {
        stats.totalEstimatedHours += project.estimatedHours;
      }
      if (project.actualHours) {
        stats.totalActualHours += project.actualHours;
      }

      // Sum progress for average
      totalProgress += Number(project.progress);
    });

    // Calculate average progress
    if (projects.length > 0) {
      stats.averageProgress = totalProgress / projects.length;
    }

    return stats;
  }

  /**
   * Update project progress based on task completion
   */
  async updateProjectProgress(projectId: string, tenantId: string): Promise<void> {
    await prisma.$transaction(async (tx) => {
      // Get all tasks for the project
      const tasks = await tx.projectTask.findMany({
        where: {
          projectId,
          tenantId,
        },
        select: {
          status: true,
        },
      });

      if (tasks.length === 0) {
        return; // No tasks, keep current progress
      }

      // Calculate progress based on completed tasks
      const completedTasks = tasks.filter(task => task.status === 'done').length;
      const progress = (completedTasks / tasks.length) * 100;

      // Update project progress
      await tx.project.update({
        where: {
          id: projectId,
        },
        data: {
          progress: new Decimal(progress),
        },
      });
    });
  }

  /**
   * Get projects that need attention (overdue, at risk, etc.)
   */
  async getProjectsNeedingAttention(tenantId: string): Promise<{
    overdue: ProjectWithDetails[];
    atRisk: ProjectWithDetails[];
    noActivity: ProjectWithDetails[];
  }> {
    const now = new Date();
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    // Get overdue projects
    const overdue = await prisma.project.findMany({
      where: {
        tenantId,
        endDate: {
          lt: now,
        },
        status: {
          in: ['planning', 'in_progress'],
        },
      },
      include: this.includeOptions,
    });

    // Get at-risk projects (due within a week and low progress)
    const atRisk = await prisma.project.findMany({
      where: {
        tenantId,
        endDate: {
          gte: now,
          lte: new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000),
        },
        progress: {
          lt: 75,
        },
        status: {
          in: ['planning', 'in_progress'],
        },
      },
      include: this.includeOptions,
    });

    // Get projects with no recent activity
    const noActivity = await prisma.project.findMany({
      where: {
        tenantId,
        updatedAt: {
          lt: oneWeekAgo,
        },
        status: {
          in: ['planning', 'in_progress'],
        },
      },
      include: this.includeOptions,
    });

    return {
      overdue: overdue as ProjectWithDetails[],
      atRisk: atRisk as ProjectWithDetails[],
      noActivity: noActivity as ProjectWithDetails[],
    };
  }

  /**
   * Trigger project creation notification
   */
  private async triggerProjectCreationNotification(
    project: ProjectWithDetails,
    tenantId: string
  ): Promise<void> {
    try {
      const targetUsers = [];

      // Notify project manager if assigned
      if (project.managerId) {
        targetUsers.push(project.managerId);
      }

      // Notify team members with project management permissions
      // TODO: Add logic to get team members with appropriate permissions

      // Create notification for each target user
      for (const userId of targetUsers) {
        await this.notificationService.createNotification({
          tenantId,
          userId,
          type: 'project_created',
          category: 'general',
          title: 'New Project Created',
          message: `Project "${project.name}" has been created and assigned to you`,
          data: {
            projectId: project.id,
            projectName: project.name,
            projectStatus: project.status,
            projectPriority: project.priority,
            projectBudget: project.budget?.toString(),
            startDate: project.startDate?.toISOString(),
            endDate: project.endDate?.toISOString(),
            opportunityId: project.opportunityId,
            opportunityTitle: project.opportunity?.title,
            estimatedHours: project.estimatedHours
          },
          actionUrl: `/projects/${project.id}`,
          actionLabel: 'View Project'
        });
      }
    } catch (error) {
      console.error('Failed to send project creation notification:', error);
      // Don't throw error to avoid breaking the main operation
    }
  }

  /**
   * Handle notifications for project updates
   */
  private async handleProjectUpdateNotifications(
    updatedProject: ProjectWithDetails,
    previousProject: any,
    tenantId: string
  ): Promise<void> {
    try {
      // Check for manager change
      if (updatedProject.managerId !== previousProject.managerId) {
        await this.triggerProjectAssignmentNotification(
          updatedProject,
          tenantId,
          previousProject.managerId
        );
      }

      // Check for status change
      if (updatedProject.status !== previousProject.status) {
        await this.triggerProjectStatusChangeNotification(
          updatedProject,
          tenantId,
          previousProject.status
        );
      }

      // Check for priority change to urgent
      if (updatedProject.priority === 'urgent' && previousProject.priority !== 'urgent') {
        await this.triggerUrgentProjectNotification(updatedProject, tenantId);
      }
    } catch (error) {
      console.error('Failed to handle project update notifications:', error);
    }
  }

  /**
   * Trigger project assignment notification
   */
  private async triggerProjectAssignmentNotification(
    project: ProjectWithDetails,
    tenantId: string,
    previousManagerId?: string | null
  ): Promise<void> {
    try {
      // Notify new manager
      if (project.managerId) {
        await this.notificationService.createNotification({
          tenantId,
          userId: project.managerId,
          type: 'project_assigned',
          category: 'general',
          title: 'Project Assigned to You',
          message: `Project "${project.name}" has been assigned to you as manager`,
          data: {
            projectId: project.id,
            projectName: project.name,
            projectStatus: project.status,
            projectPriority: project.priority,
            projectBudget: project.budget?.toString(),
            previousManager: previousManagerId
          },
          actionUrl: `/projects/${project.id}`,
          actionLabel: 'View Project'
        });
      }

      // Notify previous manager if different
      if (previousManagerId && previousManagerId !== project.managerId) {
        await this.notificationService.createNotification({
          tenantId,
          userId: previousManagerId,
          type: 'project_reassigned',
          category: 'general',
          title: 'Project Reassigned',
          message: `Project "${project.name}" has been reassigned to another manager`,
          data: {
            projectId: project.id,
            projectName: project.name,
            newManager: project.manager ? `${project.manager.firstName} ${project.manager.lastName}` : 'Unknown'
          },
          actionUrl: `/projects/${project.id}`,
          actionLabel: 'View Project'
        });
      }
    } catch (error) {
      console.error('Failed to send project assignment notification:', error);
    }
  }

  /**
   * Trigger project status change notification
   */
  private async triggerProjectStatusChangeNotification(
    project: ProjectWithDetails,
    tenantId: string,
    previousStatus: string
  ): Promise<void> {
    try {
      const targetUsers = [];

      // Notify project manager
      if (project.managerId) {
        targetUsers.push(project.managerId);
      }

      // Notify team members
      // TODO: Add logic to get project team members

      // Determine notification type based on status change
      let notificationType = 'project_status_changed';
      let title = 'Project Status Updated';
      let message = `Project "${project.name}" status changed from ${previousStatus} to ${project.status}`;

      if (project.status === 'completed') {
        notificationType = 'project_completed';
        title = 'Project Completed! 🎉';
        message = `Congratulations! Project "${project.name}" has been completed`;
      } else if (project.status === 'on_hold') {
        notificationType = 'project_on_hold';
        title = 'Project On Hold';
        message = `Project "${project.name}" has been put on hold`;
      } else if (project.status === 'cancelled') {
        notificationType = 'project_cancelled';
        title = 'Project Cancelled';
        message = `Project "${project.name}" has been cancelled`;
      }

      for (const userId of targetUsers) {
        await this.notificationService.createNotification({
          tenantId,
          userId,
          type: notificationType,
          category: 'general',
          title,
          message,
          data: {
            projectId: project.id,
            projectName: project.name,
            previousStatus,
            newStatus: project.status,
            projectPriority: project.priority,
            projectProgress: project.progress?.toString()
          },
          actionUrl: `/projects/${project.id}`,
          actionLabel: 'View Project'
        });
      }
    } catch (error) {
      console.error('Failed to send project status change notification:', error);
    }
  }

  /**
   * Trigger urgent project notification
   */
  private async triggerUrgentProjectNotification(
    project: ProjectWithDetails,
    tenantId: string
  ): Promise<void> {
    try {
      const targetUsers = [];
      if (project.managerId) {
        targetUsers.push(project.managerId);
      }

      // Notify senior management
      // TODO: Add logic to get senior management team

      for (const userId of targetUsers) {
        await this.notificationService.createNotification({
          tenantId,
          userId,
          type: 'urgent_project_alert',
          category: 'general',
          title: 'Urgent Project Alert',
          message: `Urgent project "${project.name}" requires immediate attention`,
          data: {
            projectId: project.id,
            projectName: project.name,
            projectPriority: project.priority,
            projectStatus: project.status,
            endDate: project.endDate?.toISOString(),
            projectBudget: project.budget?.toString()
          },
          actionUrl: `/projects/${project.id}`,
          actionLabel: 'View Project'
        });
      }
    } catch (error) {
      console.error('Failed to send urgent project notification:', error);
    }
  }
}
