# Dependencies
node_modules
npm-debug.log
yarn-debug.log
yarn-error.log

# Next.js build output
.next
out

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Version control
.git
.gitignore

# IDE files
.idea
.vscode
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Docker files
deployment/docker/Dockerfile
deployment/docker/docker-compose.yml
deployment/docker/.dockerignore

# Documentation
README.md
LICENSE
*.md
.github
docs

# Build cache
.swc
.turbo
tsconfig.tsbuildinfo

# Uploads directory (will be mounted as volume)
uploads/*
!uploads/.gitkeep

# Development files
.eslintcache
coverage
.nyc_output

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.test

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Temporary folders
tmp/
temp/

# Archive folders
scripts/archive/

# Deployment scripts (not needed in container)
deploy-docker.bat
deployment/scripts/
