'use client';

import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionResource, PermissionAction } from '@/types';
import { ProjectList } from '@/components/projects/ProjectList';
import { PageLayout } from '@/components/layout/page-layout';
import { Card, CardContent } from '@/components/ui/card';
import { EmptyState } from '@/components/ui/empty-state';
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline';

export default function AllProjectsPage() {
  return (
    <PermissionGate
      resource={PermissionResource.PROJECTS}
      action={PermissionAction.READ}
      fallback={
        <PageLayout>
          <Card>
            <CardContent className="p-8">
              <EmptyState
                icon={<ExclamationTriangleIcon className="w-12 h-12 text-muted-foreground" />}
                title="Access Denied"
                description="You don't have permission to view projects. Please contact your administrator for access."
                action={{
                  label: 'Back to Dashboard',
                  onClick: () => window.location.href = '/dashboard',
                  variant: 'primary'
                }}
              />
            </CardContent>
          </Card>
        </PageLayout>
      }
    >
      <PageLayout>
        <ProjectList />
      </PageLayout>
    </PermissionGate>
  );
}
