import { prisma } from '@/lib/prisma';
import { aiService } from '@/services/ai-service';
import { geminiFileService } from '@/services/gemini-file-service';
import {
  ProposalGenerationRequest,
  ProposalGenerationResponse,
  SectionGenerationRequest,
  SectionGenerationResponse,
  ChartGenerationRequest,
  ChartGenerationResponse,
  ProposalSectionType,
  ProposalChartType,
  GeminiFileContext,
  GeminiFileUploadBatchRequest
} from '@/types/proposal';
import { AIRequest } from '@/services/ai-service';

export class AIProposalGenerationService {
  constructor() {
    // Validate that Google AI API key is configured
    const apiKey = process.env.GOOGLE_AI_API_KEY;
    if (!apiKey) {
      throw new Error('Google AI API key not configured');
    }
  }

  /**
   * Start AI-powered proposal generation workflow
   */
  async generateProposal(
    request: ProposalGenerationRequest,
    tenantId: string,
    userId: string
  ): Promise<ProposalGenerationResponse> {
    try {
      // Create proposal record
      const proposal = await prisma.proposal.create({
        data: {
          tenantId,
          opportunityId: request.opportunityId,
          title: request.title,
          isAiGenerated: true,
          aiProvider: 'google',
          aiModel: 'gemini-1.5-pro',
          generationStatus: 'generating',
          selectedFileIds: request.selectedFileIds,
          locale: request.locale || 'en',
          createdById: userId
        }
      });

      // Upload files to Gemini API for context
      const geminiFileUris: Record<string, string> = {};
      let fileContexts: GeminiFileContext[] = [];

      if (request.selectedFileIds.length > 0) {
        try {
          const uploadRequest: GeminiFileUploadBatchRequest = {
            documentIds: request.selectedFileIds,
            tenantId
          };

          const uploadResult = await geminiFileService.uploadFilesBatch(uploadRequest);

          // Build file URI mapping
          uploadResult.successful.forEach(file => {
            const document = request.selectedFileIds.find(id =>
              uploadResult.successful.some(f => f.name.includes(id))
            );
            if (document) {
              geminiFileUris[document] = file.fileUri;
            }
          });

          // Update proposal with Gemini file URIs
          await prisma.proposal.update({
            where: { id: proposal.id },
            data: { geminiFileUris }
          });

          // Get file contexts for AI generation
          fileContexts = await geminiFileService.getFileContexts(
            request.selectedFileIds,
            tenantId,
            geminiFileUris
          );

        } catch (error) {
          console.error('Gemini file upload error:', error);
          // Continue with generation without file context
          fileContexts = await this.getFileContexts(
            request.selectedFileIds,
            tenantId
          );
        }
      }

      // Update proposal with selected file IDs
      await prisma.proposal.update({
        where: { id: proposal.id },
        data: {
          selectedFileIds: request.selectedFileIds
        }
      });

      // Generate sections in parallel
      const sectionPromises = request.sections.map(sectionType => {
        // Use section-specific custom prompt if available, otherwise use global custom prompt
        const sectionCustomPrompt = request.sectionCustomPrompts?.[sectionType] || request.customPrompt;
        return this.generateSection({
          proposalId: proposal.id,
          sectionType,
          customPrompt: sectionCustomPrompt
        }, tenantId, userId, fileContexts, request.locale || 'en');
      });

      // Generate charts if requested
      const chartPromises = request.includeCharts && request.chartTypes
        ? request.chartTypes.map(chartType =>
            this.generateChart({
              proposalId: proposal.id,
              chartType,
              title: `${chartType.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())} Diagram`,
              description: `AI-generated ${chartType} diagram for the proposal`
            }, tenantId, userId, fileContexts)
          )
        : [];

      // Wait for all generations to complete
      await Promise.all([...sectionPromises, ...chartPromises]);

      // Update proposal status
      await prisma.proposal.update({
        where: { id: proposal.id },
        data: {
          generationStatus: 'completed'
        }
      });

      return {
        proposalId: proposal.id,
        status: 'completed',
        message: 'Proposal generated successfully',
        estimatedCompletionTime: 5
      };

    } catch (error) {
      console.error('Proposal generation error:', error);
      throw new Error(`Failed to generate proposal: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get file contexts for AI generation (simplified approach)
   */
  private async getFileContexts(
    documentIds: string[],
    tenantId: string
  ): Promise<GeminiFileContext[]> {
    const documents = await prisma.document.findMany({
      where: {
        id: { in: documentIds },
        tenantId
      }
    });

    return documents.map((doc) => ({
      fileUri: `file://${doc.filePath}`, // Simplified URI for now
      documentId: doc.id,
      name: doc.name
    }));
  }

  /**
   * Generate a specific section of the proposal
   */
  async generateSection(
    request: SectionGenerationRequest,
    tenantId: string,
    userId: string,
    fileContexts?: GeminiFileContext[],
    locale: 'en' | 'ar' = 'en'
  ): Promise<SectionGenerationResponse> {
    try {
      // Get proposal and opportunity context
      const proposal = await prisma.proposal.findUnique({
        where: { id: request.proposalId },
        include: {
          opportunity: {
            include: {
              company: true,
              contact: true
            }
          }
        }
      });

      if (!proposal) {
        throw new Error('Proposal not found');
      }

      // Build section-specific prompt
      const prompt = await this.buildSectionPrompt(
        request.sectionType,
        proposal,
        fileContexts,
        request.customPrompt,
        locale
      );

      // Generate content using AI service
      const fileUris = fileContexts?.filter(ctx => ctx.fileUri && ctx.uploadStatus === 'active').map(ctx => ctx.fileUri) || [];

      const systemPrompt = await this.getSectionSystemPrompt(request.sectionType, tenantId, locale);

      const aiRequest: AIRequest = {
        prompt,
        systemPrompt,
        fileUris: fileUris.length > 0 ? fileUris : undefined,
        context: {
          proposalId: request.proposalId,
          sectionType: request.sectionType,
          opportunity: proposal.opportunity,
          fileContexts,
          locale
        },
        tenantId,
        userId,
        feature: 'proposal_generation'
      };

      const response = await aiService.generateResponse(aiRequest, {
        temperature: 0.7,
        maxTokens: 2000
      });

      // If this is a regeneration request, return content without creating a new record
      if (request.regenerate) {
        return {
          sectionId: '', // Will be set by the calling code
          content: response.content,
          wordCount: this.countWords(response.content),
          tokensUsed: response.tokensUsed,
          confidence: 0.85 // Default confidence score
        };
      }

      // Create section record (only for new sections)
      const orderIndex = await this.getSectionOrder(request.sectionType, tenantId);
      const title = await this.getSectionTitle(request.sectionType, tenantId);
      const section = await prisma.proposalSection.create({
        data: {
          tenantId,
          proposalId: request.proposalId,
          sectionType: request.sectionType,
          title,
          content: response.content,
          orderIndex,
          isAiGenerated: true,
          aiPrompt: prompt,
          aiProvider: response.provider,
          aiModel: response.model,
          tokensUsed: response.tokensUsed,
          generatedAt: new Date(),
          status: 'generated',
          wordCount: this.countWords(response.content),
          createdById: userId
        }
      });

      return {
        sectionId: section.id,
        content: response.content,
        wordCount: section.wordCount || 0,
        tokensUsed: response.tokensUsed,
        confidence: 0.85 // Default confidence score
      };

    } catch (error) {
      console.error('Section generation error:', error);
      throw new Error(`Failed to generate section: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate a Mermaid chart for the proposal
   */
  async generateChart(
    request: ChartGenerationRequest,
    tenantId: string,
    userId: string,
    fileContexts?: GeminiFileContext[]
  ): Promise<ChartGenerationResponse> {
    try {
      // Get proposal context
      const proposal = await prisma.proposal.findUnique({
        where: { id: request.proposalId },
        include: {
          opportunity: {
            include: {
              company: true,
              contact: true
            }
          }
        }
      });

      if (!proposal) {
        throw new Error('Proposal not found');
      }

      // Build chart-specific prompt
      const prompt = this.buildChartPrompt(
        request.chartType,
        proposal,
        fileContexts,
        request.customPrompt
      );

      // Generate Mermaid code using AI service
      const fileUris = fileContexts?.filter(ctx => ctx.fileUri && ctx.uploadStatus === 'active').map(ctx => ctx.fileUri) || [];

      const aiRequest: AIRequest = {
        prompt,
        systemPrompt: this.getChartSystemPrompt(request.chartType),
        fileUris: fileUris.length > 0 ? fileUris : undefined,
        context: {
          proposalId: request.proposalId,
          chartType: request.chartType,
          opportunity: proposal.opportunity,
          fileContexts
        },
        tenantId,
        userId,
        feature: 'mermaid_generation'
      };

      const response = await aiService.generateResponse(aiRequest, {
        temperature: 0.6,
        maxTokens: 1500
      });

      // Extract Mermaid code from response
      const mermaidCode = this.extractMermaidCode(response.content);

      // Create chart record
      const chart = await prisma.proposalChart.create({
        data: {
          tenantId,
          proposalId: request.proposalId,
          chartType: request.chartType,
          title: request.title,
          description: request.description,
          mermaidCode,
          isAiGenerated: true,
          aiPrompt: prompt,
          aiProvider: response.provider,
          aiModel: response.model,
          generatedAt: new Date(),
          status: 'generated',
          orderIndex: this.getChartOrder(request.chartType),
          createdById: userId
        }
      });

      return {
        chartId: chart.id,
        mermaidCode,
        title: request.title,
        description: request.description
      };

    } catch (error) {
      console.error('Chart generation error:', error);
      throw new Error(`Failed to generate chart: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get content section from database
   */
  private async getContentSection(sectionType: ProposalSectionType, tenantId: string) {
    try {
      // Use raw query until Prisma client is regenerated
      const result = await prisma.$queryRaw`
        SELECT * FROM proposal_content_sections
        WHERE tenant_id = ${tenantId}
        AND section_type = ${sectionType}
        AND is_active = true
        LIMIT 1
      ` as any[];

      return result.length > 0 ? {
        id: result[0].id,
        tenantId: result[0].tenant_id,
        sectionType: result[0].section_type,
        title: result[0].title,
        description: result[0].description,
        promptEn: result[0].prompt_en,
        promptAr: result[0].prompt_ar,
        systemPromptEn: result[0].system_prompt_en,
        systemPromptAr: result[0].system_prompt_ar,
        orderIndex: result[0].order_index,
        isActive: result[0].is_active,
        isDefault: result[0].is_default,
        createdById: result[0].created_by,
        createdAt: result[0].created_at,
        updatedAt: result[0].updated_at
      } : null;
    } catch (error) {
      console.error('Error fetching content section:', error);
      return null;
    }
  }

  /**
   * Build section-specific prompt with file context
   */
  private async buildSectionPrompt(
    sectionType: ProposalSectionType,
    proposal: any,
    fileContexts?: GeminiFileContext[],
    customPrompt?: string,
    locale: 'en' | 'ar' = 'en'
  ): Promise<string> {
    // Get content section from database
    const contentSection = await this.getContentSection(sectionType, proposal.tenantId);

    const baseContext = `
Project: ${proposal.title}
Company: ${proposal.opportunity?.company?.name || 'Unknown'}
Industry: ${proposal.opportunity?.company?.industry || 'Unknown'}
Project Value: ${proposal.opportunity?.value ? `$${proposal.opportunity.value}` : 'Not specified'}
Description: ${proposal.opportunity?.description || 'No description provided'}
`;

    const fileContext = fileContexts?.length
      ? `\n\nReference Documents:\n${fileContexts.map(ctx => `- ${ctx.name}${ctx.fileUri ? ` (File URI: ${ctx.fileUri})` : ''}`).join('\n')}`
      : '';

    // Use database content section if available, otherwise fall back to hardcoded prompts
    let sectionPrompt: string;
    if (contentSection) {
      sectionPrompt = locale === 'ar' && contentSection.promptAr
        ? contentSection.promptAr
        : contentSection.promptEn;
    } else {
      // Fallback to hardcoded prompts
      const fallbackPrompts = locale === 'ar' ? {
        executive_summary: `اكتب ملخصاً تنفيذياً لهذا الاقتراح لتطوير البرمجيات. اشمل القيمة التجارية والفوائد الرئيسية ونظرة عامة على الحل. استخدم المستندات المرجعية لفهم المتطلبات وتخصيص الاستجابة وفقاً لذلك.`,
        scope_of_work: `حدد نطاق العمل بما في ذلك المخرجات والميزات والمتطلبات التقنية. اجعل استجابتك مبنية على المستندات المرجعية وسياق المشروع المقدم.`,
        architecture_high_level: `اوصف الهيكل المعماري عالي المستوى بما في ذلك المكونات الرئيسية وتدفق البيانات ومجموعة التقنيات. ارجع إلى المستندات المقدمة للمتطلبات المحددة.`,
        architecture_low_level: `قدم هيكلاً معمارياً تقنياً مفصلاً بما في ذلك التقنيات والأطر وقواعد البيانات وتفاصيل التنفيذ. استخدم المستندات المرجعية لضمان التوافق مع المتطلبات.`,
        out_of_scope: `اذكر العناصر التي لا تشملها هذا الاقتراح. كن محدداً وواضحاً لتجنب توسع النطاق.`,
        assumptions: `اذكر الافتراضات المتخذة أثناء إنشاء الاقتراح بما في ذلك الافتراضات التقنية والتجارية والمشروع. اجعل هذه مبنية على سياق المشروع والمستندات المرجعية.`,
        project_plan: `أنشئ جدولاً زمنياً للمشروع مع المراحل والمعالم والمخرجات. اعتبر نطاق المشروع والمتطلبات من المستندات المرجعية.`,
        resource_estimation: `قدم تقدير الموارد بالأشهر الشخصية للأدوار المختلفة (المطورين والمصممين وضمان الجودة وغيرها) مع التبرير بناءً على نطاق المشروع.`
      } : {
        executive_summary: `Write an executive summary for this software development proposal. Include business value, key benefits, and solution overview. Use the reference documents to understand requirements and tailor the response accordingly.`,
        scope_of_work: `Define the scope of work including deliverables, features, and technical requirements. Base your response on the reference documents and project context provided.`,
        architecture_high_level: `Describe the high-level system architecture including major components, data flow, and technology stack. Reference the documents provided for specific requirements.`,
        architecture_low_level: `Provide detailed technical architecture including technologies, frameworks, databases, and implementation details. Use the reference documents to ensure alignment with requirements.`,
        out_of_scope: `List items that are NOT included in this proposal. Be specific and clear to avoid scope creep.`,
        assumptions: `List assumptions made during proposal creation including technical, business, and project assumptions. Base these on the project context and reference documents.`,
        project_plan: `Create a project timeline with phases, milestones, and deliverables. Consider the project scope and requirements from the reference documents.`,
        resource_estimation: `Provide resource estimation in person-months for different roles (developers, designers, QA, etc.) with justification based on project scope.`
      };
      sectionPrompt = fallbackPrompts[sectionType] || 'Generate content for this proposal section.';
    }

    const requirements = locale === 'ar' ? `
المتطلبات:
- قدم المحتوى فقط لهذا القسم
- لا تشمل عبارات محادثة مثل "إليك مسودة" أو "سأقوم بإنشاء"
- اكتب بأسلوب مهني ومباشر
- استخدم المستندات المرجعية كسياق لتأسيس استجابتك
- تأكد من الدقة والصلة بمتطلبات المشروع المحددة
- اكتب باللغة العربية بوضوح ودقة
- استخدم تنسيق الجداول المناسب بناءً على نوع القسم في اضيق الحدود وعند الحاجه فقط

تنسيق الجداول:
- استخدم تنسيق جداول Markdown مع خطوط عمودية (|)
- ابدأ وانته كل صف بخط عمودي
- أضف صف فاصل بعد العناوين مع شرطات (---)
- مثال:
| العنصر | التكلفة | النسبة |
|--------|---------|--------|
| التطوير | $100,000 | 50% |
| الاختبار | $50,000 | 25% |` : `
Requirements:
- Provide only the content for this section
- Do not include conversational phrases like "Here's a draft" or "I'll create"
- Write in a professional, direct style
- Use the reference documents as context to ground your response
- Ensure accuracy and relevance to the specific project requirements
- Use table formatting when needed for better readability in urgent need only

Table Formatting:
- Use Markdown table format with pipe characters (|)
- Start and end each row with a pipe character
- Add a separator row after headers with dashes (---)
- Example:
| Component | Estimated Cost | Percentage |
|-----------|----------------|------------|
| Development | $100,000 | 50% |
| Testing | $50,000 | 25% |`;

    return `${baseContext}${fileContext}

Task: ${customPrompt || sectionPrompt}
${requirements}`;
  }

  // Helper methods for prompts, titles, and ordering...
  private async getSectionSystemPrompt(sectionType: ProposalSectionType, tenantId: string, locale: 'en' | 'ar' = 'en'): Promise<string> {
    // Get content section from database
    const contentSection = await this.getContentSection(sectionType, tenantId);

    if (contentSection) {
      return locale === 'ar' && contentSection.systemPromptAr
        ? contentSection.systemPromptAr
        : contentSection.systemPromptEn;
    }

    // Fallback to hardcoded system prompts
    const systemPrompts = locale === 'ar' ? {
      executive_summary: 'أنت كاتب اقتراحات. اكتب ملخصات تنفيذية موجزة ومقنعة ومركزة على القيمة التجارية. قدم المحتوى فقط دون مقدمات محادثة.    ',
      scope_of_work: 'أنت محلل تقني. حدد نطاق المشروع بمخرجات ومتطلبات واضحة. قدم المحتوى فقط دون نص توضيحي.    ',
      architecture_high_level: 'أنت مهندس أنظمة. اوصف الهيكل المعماري عالي المستوى بعلاقات مكونات واضحة. قدم المحتوى فقط دون عبارات محادثة.    ',
      architecture_low_level: 'أنت مهندس معماري تقني. قدم مواصفات تقنية مفصلة وتفاصيل التنفيذ. قدم المحتوى فقط دون مقدمات.    ',
      out_of_scope: 'أنت مدير مشروع. اذكر الاستثناءات بوضوح وتحديد. قدم المحتوى فقط دون نص محادثة.    ',
      assumptions: 'أنت محلل أعمال. اذكر افتراضات المشروع بوضوح ومنطق. قدم المحتوى فقط دون عبارات توضيحية.    ',
      project_plan: 'أنت مدير مشروع. أنشئ جداول زمنية منظمة بمراحل ومعالم واضحة. قدم المحتوى فقط دون نص محادثة.    ',
      resource_estimation: 'أنت مخطط موارد. قدم تقديرات موارد دقيقة بتبريرات واضحة. قدم المحتوى فقط دون مقدمات.    '
    } : {
      executive_summary: 'You are a proposal writer. Write executive summaries that are concise, compelling, and focused on business value. Provide only the content without conversational preambles. ',
      scope_of_work: 'You are a technical analyst. Define project scope with clear deliverables and requirements. Provide only the content without explanatory text.  ',
      architecture_high_level: 'You are a system architect. Describe high-level architecture with clear component relationships. Provide only the content without conversational phrases.  ',
      architecture_low_level: 'You are a technical architect. Provide detailed technical specifications and implementation details. Provide only the content without preambles.  ',
      out_of_scope: 'You are a project manager. List exclusions clearly and specifically. Provide only the content without conversational text.  ',
      assumptions: 'You are a business analyst. List project assumptions clearly and logically. Provide only the content without explanatory phrases.  ',
      project_plan: 'You are a project manager. Create structured timelines with clear phases and milestones. Provide only the content without conversational text.  ',
      resource_estimation: 'You are a resource planner. Provide accurate resource estimates with clear justifications. Provide only the content without preambles.  '
    };

    return systemPrompts[sectionType] || (locale === 'ar'
      ? 'أنت كاتب اقتراحات محترف. قدم محتوى واضح ومباشر دون عبارات محادثة أو مقدمات.    '
      : 'You are a professional proposal writer. Provide clear, direct content without conversational phrases or preambles.  ');
  }

  private getChartSystemPrompt(chartType: ProposalChartType): string {
    const chartTypeFormatted = chartType.replace(/_/g, ' ');
    return `You are an expert system architect specializing in creating professional diagrams for business documents.

Generate valid Mermaid diagram syntax for ${chartTypeFormatted} diagrams that are optimized for Word documents with the following requirements:

DOCUMENT OPTIMIZATION:
- Create diagrams that fit well within standard Word document pages (8.5" x 11")
- Use horizontal layouts (LR) when possible to maximize width utilization
- Limit diagram width to prevent horizontal scrolling in documents
- Ensure proper spacing and alignment for professional presentation

LAYOUT REQUIREMENTS:
- Use clear, well-aligned node positioning
- Maintain consistent spacing between elements
- Create balanced, symmetrical layouts when possible
- Avoid overcrowded or cluttered arrangements

CONTENT STANDARDS:
- Use concise, professional node labels (max 3-4 words per label)
- Avoid special characters, quotes, or parentheses in labels
- Use simple, clean text that renders well in documents
- Ensure all connections are logical and easy to follow

OUTPUT FORMAT:
- Provide only the Mermaid code without explanations or markdown formatting
- Start with appropriate diagram type and direction (e.g., "graph LR" or "flowchart TD")
- Use consistent node styling throughout the diagram`;
  }

  private async getSectionTitle(sectionType: ProposalSectionType, tenantId: string): Promise<string> {
    // Try to get title from content section in database
    const contentSection = await this.getContentSection(sectionType, tenantId);
    if (contentSection) {
      return contentSection.title;
    }

    // Fallback to hardcoded titles for backward compatibility
    const titles: Record<string, string> = {
      executive_summary: 'Executive Summary',
      scope_of_work: 'Scope of Work',
      architecture_high_level: 'High-Level Architecture',
      architecture_low_level: 'Detailed Technical Architecture',
      out_of_scope: 'Out of Scope',
      assumptions: 'Assumptions and Constraints',
      project_plan: 'Project Plan and Timeline',
      resource_estimation: 'Resource Estimation'
    };
    return titles[sectionType] || sectionType.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  }

  private async getSectionOrder(sectionType: ProposalSectionType, tenantId: string): Promise<number> {
    // Try to get order from content section in database
    const contentSection = await this.getContentSection(sectionType, tenantId);
    if (contentSection) {
      return contentSection.orderIndex;
    }

    // Fallback to hardcoded order for backward compatibility
    const order: Record<string, number> = {
      executive_summary: 1,
      scope_of_work: 2,
      architecture_high_level: 3,
      architecture_low_level: 4,
      out_of_scope: 5,
      assumptions: 6,
      project_plan: 7,
      resource_estimation: 8
    };
    return order[sectionType] || 999; // Default high order for unknown sections
  }

  private getChartOrder(chartType: ProposalChartType): number {
    const order: Record<string, number> = {
      architecture_high_level: 1,
      architecture_low_level: 2,
      system_flow: 3,
      project_timeline: 4,
      deployment_diagram: 5
    };
    return order[chartType] || 999; // Default high order for unknown chart types
  }

  private buildChartPrompt(
    chartType: ProposalChartType,
    proposal: any,
    fileContexts?: GeminiFileContext[],
    customPrompt?: string
  ): string {
    const baseContext = `
Project: ${proposal.title}
Company: ${proposal.opportunity?.company?.name || 'Unknown'}
Industry: ${proposal.opportunity?.company?.industry || 'Unknown'}
`;

    // Add file context if available
    const fileContext = fileContexts?.length
      ? `\n\nReference Documents:\n${fileContexts.map(ctx => `- ${ctx.name}${ctx.fileUri ? ` (File URI: ${ctx.fileUri})` : ''}`).join('\n')}`
      : '';

    const chartPrompts = {
      architecture_high_level: 'Generate a high-level system architecture Mermaid diagram showing major components and their relationships. Focus on 5-8 main components with clear connections. Use horizontal layout (LR) for better Word document fit.',
      architecture_low_level: 'Generate a detailed technical architecture Mermaid diagram with specific technologies and data flow. Include 8-12 components with clear data flow paths. Use top-down layout (TD) with proper alignment.',
      project_timeline: 'Generate a project timeline Mermaid Gantt chart showing phases, milestones, and dependencies. Include 4-6 major phases with realistic timelines. Ensure proper date formatting and clear milestone markers.',
      system_flow: 'Generate a system flow Mermaid diagram showing user interactions and data processing flow. Include 6-10 process steps with decision points. Use flowchart format with clear directional flow.'
    };

    return `${baseContext}${fileContext}

Task: ${customPrompt || chartPrompts[chartType]}

WORD DOCUMENT OPTIMIZATION REQUIREMENTS:
- Create diagrams optimized for standard Word document pages (8.5" x 11")
- Use appropriate layout direction for the chart type:
  * Architecture diagrams: Use "graph LR" (left-to-right) for better width utilization
  * Process flows: Use "flowchart TD" (top-down) for logical flow
  * Timelines: Use "gantt" format with proper date ranges
- Limit to 6-12 nodes maximum to prevent overcrowding
- Ensure balanced, symmetrical layout with proper spacing

CONTENT AND STYLING REQUIREMENTS:
- Use concise, professional labels (2-4 words maximum per node)
- Avoid special characters, quotes, parentheses, or symbols in labels
- Use consistent node shapes throughout the diagram
- Ensure all text is readable and professional
- Create logical, easy-to-follow connections

TECHNICAL REQUIREMENTS:
- Generate only valid Mermaid syntax without explanations
- Do not include markdown formatting or conversational text
- Start with appropriate diagram declaration (graph LR, flowchart TD, gantt, etc.)
- Use consistent styling and formatting throughout
- Ensure the diagram renders properly in document formats`;
  }

  private extractMermaidCode(content: string): string {
    // Extract Mermaid code from AI response
    const mermaidMatch = content.match(/```mermaid\n([\s\S]*?)\n```/);
    if (mermaidMatch) {
      return mermaidMatch[1].trim();
    }
    
    // If no code block, assume the entire content is Mermaid code
    return content.trim();
  }

  private countWords(text: string): number {
    return text.trim().split(/\s+/).length;
  }
}

export const aiProposalService = new AIProposalGenerationService();
