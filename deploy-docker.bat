@echo off
REM Simple Docker deployment script for CRM Platform
REM This script deploys the CRM platform to Docker with seeding

setlocal enabledelayedexpansion

REM Colors for terminal output
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

echo %BLUE%========================================%NC%
echo %BLUE%    CRM Platform Docker Deployment     %NC%
echo %BLUE%========================================%NC%
echo.

REM Get environment from user input or use default
set "ENVIRONMENT=%~1"
if "%ENVIRONMENT%"=="" set "ENVIRONMENT=development"
if "%ENVIRONMENT%"=="development" set "ENVIRONMENT=dev"

echo %GREEN%Deploying to %ENVIRONMENT% environment...%NC%
echo.

REM Check if Docker is running
docker --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo %RED%Error: Docker is not installed or not running%NC%
    echo Please install Docker Desktop and make sure it's running
    pause
    exit /b 1
)

REM Check if Docker Compose is available
docker-compose --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo %RED%Error: Docker Compose is not available%NC%
    echo Please install Docker Compose
    pause
    exit /b 1
)

REM Make sure we're in the project root
if not exist "package.json" (
    echo %RED%Error: Please run this script from the project root directory%NC%
    pause
    exit /b 1
)

REM Make docker-migrate.sh executable (if on Windows with WSL/Git Bash)
if exist "scripts\docker-migrate.sh" (
    echo %YELLOW%Making migration script executable...%NC%
    attrib -r "scripts\docker-migrate.sh" >nul 2>&1
)

REM Stop any existing containers
echo %YELLOW%Stopping existing containers...%NC%
docker-compose -f deployment\docker\docker-compose.yml down >nul 2>&1

REM Clean up old containers and images automatically
echo %YELLOW%Cleaning up old containers and images...%NC%
docker system prune -f >nul 2>&1

REM Build and start containers
echo %YELLOW%Building and starting containers...%NC%
echo This may take a few minutes on first run...
echo.

docker-compose -f deployment\docker\docker-compose.yml build --no-cache --progress=plain
if %ERRORLEVEL% neq 0 (
    echo %RED%Error: Failed to build Docker images%NC%
    pause
    exit /b 1
)

docker-compose -f deployment\docker\docker-compose.yml up -d
if %ERRORLEVEL% neq 0 (
    echo %RED%Error: Failed to start containers%NC%
    pause
    exit /b 1
)

REM Wait for containers to be ready
echo %YELLOW%Waiting for containers to be ready...%NC%
timeout /t 30 /nobreak >nul

REM Check container status
echo %YELLOW%Checking container status...%NC%
docker-compose -f deployment\docker\docker-compose.yml ps

REM Show logs for debugging
echo.
echo %YELLOW%Recent container logs:%NC%
docker-compose -f deployment\docker\docker-compose.yml logs --tail=20

echo.
echo %GREEN%========================================%NC%
echo %GREEN%    Deployment Completed Successfully!  %NC%
echo %GREEN%========================================%NC%
echo.
echo %YELLOW%Access URLs:%NC%
echo   Application: http://localhost:3010
echo   Database (pgAdmin): http://localhost:8082
echo     - Email: <EMAIL>
echo     - Password: admin
echo.
echo %YELLOW%Useful Commands:%NC%
echo   View logs: docker-compose -f deployment\docker\docker-compose.yml logs -f
echo   Stop containers: docker-compose -f deployment\docker\docker-compose.yml down
echo   Restart: docker-compose -f deployment\docker\docker-compose.yml restart
echo.
echo %BLUE%The application should be available at http://localhost:3010%NC%
echo %BLUE%Database migrations and seeding will run automatically.%NC%
echo.

pause
endlocal
