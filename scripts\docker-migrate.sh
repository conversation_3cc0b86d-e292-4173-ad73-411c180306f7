#!/bin/bash

# Docker Migration and Seeding Script
# This script handles database migrations and seeding for Docker containers

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in a Docker container
if [ ! -f /.dockerenv ]; then
    log_warning "This script is designed to run inside a Docker container"
fi

# Wait for database to be ready
wait_for_database() {
    log_info "Waiting for database to be ready..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if npx prisma db execute --stdin <<< "SELECT 1;" >/dev/null 2>&1; then
            log_success "Database is ready!"
            return 0
        fi
        
        log_info "Database not ready yet (attempt $attempt/$max_attempts). Waiting 2 seconds..."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    log_error "Database failed to become ready after $max_attempts attempts"
    return 1
}

# Run database migrations
run_migrations() {
    log_info "Running database migrations..."
    
    # Generate Prisma client first
    log_info "Generating Prisma client..."
    if npx prisma generate; then
        log_success "Prisma client generated successfully"
    else
        log_error "Failed to generate Prisma client"
        return 1
    fi
    
    # Run migrations
    log_info "Deploying database migrations..."
    if npx prisma migrate deploy; then
        log_success "Database migrations completed successfully"
        return 0
    else
        log_warning "Migration deployment failed or no migrations to apply"
        
        # Try to push schema as fallback
        log_info "Attempting to push schema as fallback..."
        if npx prisma db push --force-reset; then
            log_success "Schema pushed successfully"
            return 0
        else
            log_error "Failed to push schema"
            return 1
        fi
    fi
}

# Run database seeding
run_seeding() {
    local auto_seed=${AUTO_SEED:-false}
    
    if [ "$auto_seed" != "true" ]; then
        log_info "Auto-seeding is disabled (AUTO_SEED=$auto_seed)"
        return 0
    fi
    
    log_info "Running database seeding..."
    
    # Check if master-seed.js exists
    if [ -f "scripts/master-seed.js" ]; then
        log_info "Running master seeding script..."
        if node scripts/master-seed.js --skip-existing; then
            log_success "Database seeding completed successfully"
        else
            log_warning "Database seeding failed or data already exists"
        fi
    else
        log_warning "Master seeding script not found at scripts/master-seed.js"
        
        # Try alternative seeding methods
        if [ -f "package.json" ] && npm run | grep -q "db:seed"; then
            log_info "Running npm db:seed..."
            if npm run db:seed; then
                log_success "Database seeding completed via npm script"
            else
                log_warning "npm db:seed failed"
            fi
        else
            log_warning "No seeding method available"
        fi
    fi
}

# Main execution
main() {
    log_info "Starting Docker migration and seeding process..."
    
    # Wait for database
    if ! wait_for_database; then
        log_error "Database readiness check failed"
        exit 1
    fi
    
    # Run migrations
    if ! run_migrations; then
        log_error "Migration process failed"
        exit 1
    fi
    
    # Run seeding
    run_seeding
    
    log_success "Docker migration and seeding process completed!"
}

# Execute main function
main "$@"
