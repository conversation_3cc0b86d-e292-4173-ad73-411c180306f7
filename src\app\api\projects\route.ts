import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { ProjectManagementService, createProjectSchema } from '@/services/project-management';
import { withPermission, AuthenticatedRequest } from '@/lib/permission-middleware';
import { PermissionResource, PermissionAction } from '@/types';

const projectService = new ProjectManagementService();

// Query parameters schema for GET requests
const getProjectsQuerySchema = z.object({
  page: z.string().optional().transform(val => val ? parseInt(val, 10) : 1),
  limit: z.string().optional().transform(val => val ? parseInt(val, 10) : 10),
  status: z.string().optional(),
  priority: z.string().optional(),
  managerId: z.string().optional(),
  opportunityId: z.string().optional(),
  search: z.string().optional(),
  includeDetails: z.string().optional().transform(val => val === 'true'),
});

/**
 * GET /api/projects
 * Get all projects for the current tenant
 */
export const GET = withPermission({
  resource: PermissionResource.PROJECTS,
  action: PermissionAction.READ,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const { searchParams } = new URL(req.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    const validatedQuery = getProjectsQuerySchema.parse(queryParams);

    const result = await projectService.getProjects(req.tenantId, validatedQuery);

    return NextResponse.json({
      success: true,
      data: result,
      message: 'Projects retrieved successfully'
    });
  } catch (error) {
    console.error('Get projects error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid query parameters', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to fetch projects' },
      { status: 500 }
    );
  }
});

/**
 * POST /api/projects
 * Create a new project
 */
export const POST = withPermission({
  resource: PermissionResource.PROJECTS,
  action: PermissionAction.CREATE,
  allowSuperAdmin: true
})(async (req: AuthenticatedRequest) => {
  try {
    const body = await req.json();
    const validatedData = createProjectSchema.parse(body);

    const project = await projectService.createProject(
      req.tenantId,
      validatedData,
      req.userId
    );

    return NextResponse.json({
      success: true,
      data: { project },
      message: 'Project created successfully'
    }, { status: 201 });
  } catch (error) {
    console.error('Create project error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid project data', details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create project' },
      { status: 500 }
    );
  }
});
