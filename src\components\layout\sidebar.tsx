'use client';

import { useTranslation } from '@/components/providers/locale-provider';
import { usePathname } from 'next/navigation';
import { SmoothLink } from '@/components/ui/smooth-link';
import {
  HomeIcon,
  UsersIcon,
  BuildingOfficeIcon,
  UserGroupIcon,
  ChartBarIcon,
  DocumentTextIcon,
  FolderIcon,
  ClockIcon,
  Cog6ToothIcon,
  ShieldCheckIcon,
  CurrencyDollarIcon,
  SparklesIcon,
  BoltIcon
} from '@heroicons/react/24/outline';
import { cn } from '@/lib/utils';

const navigationItems = [
  {
    name: 'dashboard',
    href: '/',
    icon: HomeIcon,
    current: false,
  },
  {
    name: 'contacts',
    href: '/contacts',
    icon: UsersIcon,
    current: false,
  },
  {
    name: 'companies',
    href: '/companies',
    icon: BuildingOfficeIcon,
    current: false,
  },
  {
    name: 'leads',
    href: '/leads',
    icon: UserGroupIcon,
    current: false,
  },
  {
    name: 'opportunities',
    href: '/opportunities',
    icon: ChartBarIcon,
    current: false,
  },
  {
    name: 'proposals',
    href: '/proposals',
    icon: DocumentTextIcon,
    current: false,
  },
  {
    name: 'dashboard',
    href: '/projects',
    icon: FolderIcon,
    current: false,
  },
  {
    name: 'projects',
    href: '/projects/all',
    icon: FolderIcon,
    current: false,
  },
  {
    name: 'activities',
    href: '/activities',
    icon: ClockIcon,
    current: false,
  },
  {
    name: 'settings',
    href: '/settings',
    icon: Cog6ToothIcon,
    current: false,
  },
];

const adminItems = [
  {
    name: 'users',
    href: '/admin/users',
    icon: UsersIcon,
    current: false,
  },
  {
    name: 'roles',
    href: '/admin/roles',
    icon: ShieldCheckIcon,
    current: false,
  },
  {
    name: 'automation',
    href: '/admin/automation',
    icon: BoltIcon,
    current: false,
  },
  {
    name: 'billing',
    href: '/admin/billing',
    icon: CurrencyDollarIcon,
    current: false,
  },
  {
    name: 'admin_settings',
    href: '/admin/settings',
    icon: Cog6ToothIcon,
    current: false,
  },
];

export function Sidebar() {
  const { t } = useTranslation();
  const pathname = usePathname();

  return (
    <div className="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-64 lg:flex-col">
      <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-white dark:bg-gray-800 px-6 pb-4 shadow-lg">
        {/* Logo */}
        <div className="flex h-16 shrink-0 items-center">
          <div className="flex items-center space-x-3">
            <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary-600">
              <SparklesIcon className="h-5 w-5 text-white" />
            </div>
            <div>
              <h1 className="text-lg font-bold text-gray-900 dark:text-white">
                CRM Platform
              </h1>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Multi-Tenant SaaS
              </p>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex flex-1 flex-col">
          <ul role="list" className="flex flex-1 flex-col gap-y-7">
            {/* Main Navigation */}
            <li>
              <ul role="list" className="-mx-2 space-y-1">
                {navigationItems.map((item) => {
                  const isActive = pathname.includes(item.href);
                  return (
                    <li key={item.name}>
                      <SmoothLink
                        href={item.href}
                        className={cn(
                          isActive
                            ? 'bg-primary-50 text-primary-700 dark:bg-primary-900/20 dark:text-primary-300'
                            : 'text-gray-700 hover:text-primary-700 hover:bg-gray-50 dark:text-gray-300 dark:hover:text-primary-300 dark:hover:bg-gray-700',
                          'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-medium transition-colors'
                        )}
                      >
                        <item.icon
                          className={cn(
                            isActive
                              ? 'text-primary-700 dark:text-primary-300'
                              : 'text-gray-400 group-hover:text-primary-700 dark:group-hover:text-primary-300',
                            'h-6 w-6 shrink-0 transition-colors'
                          )}
                          aria-hidden="true"
                        />
                        {t(item.name)}
                      </SmoothLink>
                    </li>
                  );
                })}
              </ul>
            </li>

            {/* Admin Section */}
            <li>
              <div className="text-xs font-semibold leading-6 text-gray-400 dark:text-gray-500 uppercase tracking-wide">
                {t('admin')}
              </div>
              <ul role="list" className="-mx-2 mt-2 space-y-1">
                {adminItems.map((item) => {
                  const isActive = pathname.includes(item.href);
                  return (
                    <li key={item.name}>
                      <SmoothLink
                        href={item.href}
                        className={cn(
                          isActive
                            ? 'bg-primary-50 text-primary-700 dark:bg-primary-900/20 dark:text-primary-300'
                            : 'text-gray-700 hover:text-primary-700 hover:bg-gray-50 dark:text-gray-300 dark:hover:text-primary-300 dark:hover:bg-gray-700',
                          'group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-medium transition-colors'
                        )}
                      >
                        <item.icon
                          className={cn(
                            isActive
                              ? 'text-primary-700 dark:text-primary-300'
                              : 'text-gray-400 group-hover:text-primary-700 dark:group-hover:text-primary-300',
                            'h-6 w-6 shrink-0 transition-colors'
                          )}
                          aria-hidden="true"
                        />
                        {t(item.name)}
                      </SmoothLink>
                    </li>
                  );
                })}
              </ul>
            </li>

            {/* Subscription Status */}
            <li className="mt-auto">
              <div className="rounded-lg bg-gradient-to-r from-primary-500 to-primary-600 p-4 text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium">Professional Plan</p>
                    <p className="text-xs opacity-90">67% used this month</p>
                  </div>
                  <div className="h-8 w-8 rounded-full bg-white/20 flex items-center justify-center">
                    <ChartBarIcon className="h-4 w-4" />
                  </div>
                </div>
                <div className="mt-3">
                  <div className="h-2 bg-white/20 rounded-full">
                    <div className="h-2 bg-white rounded-full" style={{ width: '67%' }}></div>
                  </div>
                </div>
              </div>
            </li>
          </ul>
        </nav>
      </div>
    </div>
  );
}
