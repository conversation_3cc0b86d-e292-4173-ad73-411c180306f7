'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { EmptyState } from '@/components/ui/empty-state';
import { PermissionGate } from '@/components/ui/permission-gate';
import { PermissionResource, PermissionAction } from '@/types';
import { useEnhancedDeleteDialog, type RelatedData } from '@/components/ui/enhanced-delete-dialog';
import {
  Eye,
  Edit3,
  Download,
  Copy,
  Trash2,
  MoreHorizontal,
  Search,
  Filter,
  FileText,
  Clock,
  User,
  Calendar,
  BarChart3,
  RefreshCw,
  Wand2,
  Plus,
  Loader2
} from 'lucide-react';
import { formatDateSafe } from '@/lib/utils';
import { toast } from 'sonner';

interface ProposalData {
  id: string;
  title: string;
  status: string;
  version: number;
  generationStatus: string;
  createdAt: string;
  updatedAt: string;
  createdBy: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  statistics: {
    totalSections: number;
    completedSections: number;
    totalCharts: number;
    renderedCharts: number;
    completionPercentage: number;
  };
}

interface OpportunityProposalManagementProps {
  opportunityId: string;
  opportunityTitle: string;
}

interface DuplicateDialogData {
  proposalId: string;
  proposalTitle: string;
}

export function OpportunityProposalManagement({
  opportunityId,
  opportunityTitle
}: OpportunityProposalManagementProps) {
  const router = useRouter();
  const [proposals, setProposals] = useState<ProposalData[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [versionFilter, setVersionFilter] = useState('all');
  const [dateRangeFilter, setDateRangeFilter] = useState('all');
  const [duplicateDialog, setDuplicateDialog] = useState<{
    open: boolean;
    data: DuplicateDialogData | null;
  }>({ open: false, data: null });
  const [duplicateLoading, setDuplicateLoading] = useState(false);
  const [selectedProposals, setSelectedProposals] = useState<string[]>([]);
  const [bulkDeleteLoading, setBulkDeleteLoading] = useState(false);
  const [downloadingProposals, setDownloadingProposals] = useState<Set<string>>(new Set());
  const { showDeleteDialog, DialogComponent } = useEnhancedDeleteDialog();

  // Load proposals
  const loadProposals = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        search: searchTerm,
        status: statusFilter,
        version: versionFilter,
        dateRange: dateRangeFilter,
        limit: '50'
      });

      const response = await fetch(`/api/opportunities/${opportunityId}/proposals?${params}`);
      
      if (response.ok) {
        const result = await response.json();
        setProposals(result.data.proposals);
      } else {
        toast.error('Failed to load proposals');
      }
    } catch (error) {
      console.error('Error loading proposals:', error);
      toast.error('Failed to load proposals');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadProposals();
  }, [opportunityId, searchTerm, statusFilter, versionFilter, dateRangeFilter]);

  // Handle proposal deletion
  const handleDeleteProposal = async (proposalId: string, proposalTitle: string) => {
    // Find the proposal to get its statistics for the delete dialog
    const proposal = proposals.find(p => p.id === proposalId);
    const relatedData = [];

    if (proposal?.statistics) {
      if (proposal.statistics.totalSections > 0) {
        relatedData.push({
          type: 'sections',
          count: proposal.statistics.totalSections,
          label: proposal.statistics.totalSections === 1 ? 'section' : 'sections',
          description: 'content sections with text and data'
        });
      }

      if (proposal.statistics.totalCharts > 0) {
        relatedData.push({
          type: 'charts',
          count: proposal.statistics.totalCharts,
          label: proposal.statistics.totalCharts === 1 ? 'chart' : 'charts',
          description: 'diagrams and visualizations'
        });
      }
    }

    const confirmed = await showDeleteDialog({
      title: 'Delete Proposal',
      description: `Are you sure you want to delete "${proposalTitle}"? This action cannot be undone and will permanently remove all proposal content.`,
      itemName: proposalTitle,
      itemType: 'proposal',
      relatedData,
      customWarnings: [
        'All proposal sections and content will be permanently deleted.',
        'Any generated charts and diagrams will be removed.',
        'This action cannot be undone and the proposal cannot be recovered.',
        'Consider downloading the proposal before deletion if you need to keep a copy.',
        'The proposal will be removed from this opportunity permanently.'
      ],
      requireConfirmation: relatedData.length > 0,
      confirmationText: relatedData.length > 0 ? proposalTitle : undefined,
      onConfirm: async () => {
        const response = await fetch(`/api/proposals/${proposalId}`, {
          method: 'DELETE'
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to delete proposal');
        }

        toast.success('Proposal deleted successfully');
        loadProposals(); // Reload the list
      }
    });

    // The confirmed variable will be true if the deletion was successful
    // No additional handling needed as the onConfirm function handles the actual deletion
  };

  // Handle bulk proposal deletion
  const handleBulkDeleteProposals = async () => {
    if (selectedProposals.length === 0) return;

    // Calculate total related data for all selected proposals
    const selectedProposalData = proposals.filter(p => selectedProposals.includes(p.id));
    const totalSections = selectedProposalData.reduce((sum, p) => sum + (p.statistics?.totalSections || 0), 0);
    const totalCharts = selectedProposalData.reduce((sum, p) => sum + (p.statistics?.totalCharts || 0), 0);

    const relatedData: RelatedData[] = [];
    if (totalSections > 0) {
      relatedData.push({
        type: 'sections',
        count: totalSections,
        label: totalSections === 1 ? 'section' : 'sections',
        description: 'content sections with text and data'
      });
    }
    if (totalCharts > 0) {
      relatedData.push({
        type: 'charts',
        count: totalCharts,
        label: totalCharts === 1 ? 'chart' : 'charts',
        description: 'diagrams and visualizations'
      });
    }

    const confirmed = await showDeleteDialog({
      title: 'Delete Multiple Proposals',
      description: `Are you sure you want to delete ${selectedProposals.length} proposal(s)? This action cannot be undone and will permanently remove all proposal content.`,
      itemName: `${selectedProposals.length} proposal(s)`,
      itemType: 'proposals',
      relatedData,
      customWarnings: [
        `This will permanently delete ${selectedProposals.length} proposal(s) and all their content.`,
        'All proposal sections and content will be permanently deleted.',
        'Any generated charts and diagrams will be removed.',
        'This action cannot be undone and the proposals cannot be recovered.',
        'Consider downloading the proposals before deletion if you need to keep copies.'
      ],
      requireConfirmation: true,
      confirmationText: `DELETE ${selectedProposals.length} PROPOSALS`,
      onConfirm: async () => {
        setBulkDeleteLoading(true);
        try {
          const response = await fetch('/api/proposals/bulk-delete', {
            method: 'DELETE',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              proposalIds: selectedProposals
            })
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Failed to delete proposals');
          }

          const result = await response.json();
          toast.success(`Successfully deleted ${result.data.deletedCount} proposal(s)`);
          setSelectedProposals([]); // Clear selection
          loadProposals(); // Reload the list
        } finally {
          setBulkDeleteLoading(false);
        }
      }
    });
  };

  // Selection handlers
  const handleSelectProposal = (proposalId: string, checked: boolean) => {
    if (checked) {
      setSelectedProposals(prev => [...prev, proposalId]);
    } else {
      setSelectedProposals(prev => prev.filter(id => id !== proposalId));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedProposals(proposals.map(p => p.id));
    } else {
      setSelectedProposals([]);
    }
  };

  const isAllSelected = proposals.length > 0 && selectedProposals.length === proposals.length;
  const isPartiallySelected = selectedProposals.length > 0 && selectedProposals.length < proposals.length;

  // Handle proposal duplication
  const handleDuplicateProposal = async (versionType: 'minor' | 'major' | 'copy') => {
    if (!duplicateDialog.data) return;

    try {
      setDuplicateLoading(true);
      const response = await fetch(`/api/proposals/${duplicateDialog.data.proposalId}/duplicate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: duplicateDialog.data.proposalTitle,
          versionType,
          includeContent: true,
          includeCharts: true
        }),
      });

      const result = await response.json();

      if (response.ok) {
        toast.success(`Proposal ${versionType === 'copy' ? 'copied' : 'version created'} successfully`);
        setDuplicateDialog({ open: false, data: null });
        loadProposals(); // Reload the list
        
        // Navigate to the new proposal
        router.push(`/proposals/${result.data.proposal.id}`);
      } else {
        toast.error(result.error || 'Failed to duplicate proposal');
      }
    } catch (error) {
      console.error('Error duplicating proposal:', error);
      toast.error('Failed to duplicate proposal');
    } finally {
      setDuplicateLoading(false);
    }
  };

  // Handle download
  const handleDownloadProposal = async (proposalId: string) => {
    if (downloadingProposals.has(proposalId)) return;

    try {
      setDownloadingProposals(prev => new Set(prev).add(proposalId));
      toast.info('Preparing download...');

      const response = await fetch(`/api/proposals/${proposalId}/download`);

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `proposal-${proposalId}.docx`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        toast.success('Proposal downloaded successfully');
      } else {
        toast.error('Failed to download proposal');
      }
    } catch (error) {
      console.error('Error downloading proposal:', error);
      toast.error('Failed to download proposal');
    } finally {
      setDownloadingProposals(prev => {
        const newSet = new Set(prev);
        newSet.delete(proposalId);
        return newSet;
      });
    }
  };

  // Get status badge color
  const getStatusBadgeColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      case 'generating':
        return 'bg-blue-100 text-blue-800';
      case 'generated':
        return 'bg-green-100 text-green-800';
      case 'reviewed':
        return 'bg-yellow-100 text-yellow-800';
      case 'approved':
        return 'bg-emerald-100 text-emerald-800';
      case 'sent':
        return 'bg-purple-100 text-purple-800';
      case 'accepted':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Format version number
  const formatVersion = (version: number) => {
    if (version % 1 === 0) {
      return `v${version}.0`;
    }
    return `v${version.toFixed(1)}`;
  };

  // Get user display name
  const getUserDisplayName = (user: ProposalData['createdBy']) => {
    return `${user.firstName} ${user.lastName}`.trim() || user.email;
  };

  // Handle creating new AI proposal
  const handleCreateAIProposal = () => {
    router.push(`/opportunities/${opportunityId}/generate-proposal`);
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Generated Proposals</CardTitle>
          <CardDescription>
            AI-generated proposals for this opportunity
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center items-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Generated Proposals</CardTitle>
            <CardDescription>
              AI-generated proposals for {opportunityTitle}
            </CardDescription>
          </div>
          <div className="flex items-center space-x-3">
            <Badge variant="outline">
              {proposals.length} proposal{proposals.length !== 1 ? 's' : ''}
            </Badge>
            <PermissionGate
              resource={PermissionResource.PROPOSALS}
              action={PermissionAction.CREATE}
            >
              <Button
                onClick={handleCreateAIProposal}
                size="sm"
                className="flex items-center space-x-2"
              >
                <Wand2 className="h-4 w-4" />
                <span>Create AI Proposal</span>
              </Button>
            </PermissionGate>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* Filters */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="space-y-2">
            <Label htmlFor="search">Search</Label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                id="search"
                placeholder="Search proposals..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="All statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="generating">Generating</SelectItem>
                <SelectItem value="generated">Generated</SelectItem>
                <SelectItem value="reviewed">Reviewed</SelectItem>
                <SelectItem value="approved">Approved</SelectItem>
                <SelectItem value="sent">Sent</SelectItem>
                <SelectItem value="accepted">Accepted</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="version">Version</Label>
            <Select value={versionFilter} onValueChange={setVersionFilter}>
              <SelectTrigger>
                <SelectValue placeholder="All versions" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Versions</SelectItem>
                <SelectItem value="latest">Latest Only</SelectItem>
                <SelectItem value="1">v1.x</SelectItem>
                <SelectItem value="2">v2.x</SelectItem>
                <SelectItem value="3">v3.x</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="dateRange">Date Range</Label>
            <Select value={dateRangeFilter} onValueChange={setDateRangeFilter}>
              <SelectTrigger>
                <SelectValue placeholder="All time" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Time</SelectItem>
                <SelectItem value="today">Today</SelectItem>
                <SelectItem value="week">This Week</SelectItem>
                <SelectItem value="month">This Month</SelectItem>
                <SelectItem value="quarter">This Quarter</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Proposals List */}
        {proposals.length === 0 ? (
          <EmptyState
            icon={<FileText className="w-12 h-12" />}
            title="No proposals found"
            description="No proposals have been generated for this opportunity yet."
            action={{
              label: 'Create AI Proposal',
              onClick: handleCreateAIProposal,
              variant: 'primary'
            }}
          />
        ) : (
          <div className="space-y-4">
            {/* Bulk Actions Header */}
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border">
              <div className="flex items-center space-x-4">
                <Checkbox
                  checked={isAllSelected}
                  onCheckedChange={handleSelectAll}
                  className={isPartiallySelected ? "data-[state=checked]:bg-blue-600" : ""}
                />
                <span className="text-sm font-medium">
                  {selectedProposals.length === 0
                    ? `Select all ${proposals.length} proposal${proposals.length !== 1 ? 's' : ''}`
                    : `${selectedProposals.length} proposal${selectedProposals.length !== 1 ? 's' : ''} selected`
                  }
                </span>
              </div>

              {selectedProposals.length > 0 && (
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedProposals([])}
                  >
                    Clear Selection
                  </Button>
                  <PermissionGate
                    resource={PermissionResource.PROPOSALS}
                    action={PermissionAction.DELETE}
                  >
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={handleBulkDeleteProposals}
                      disabled={bulkDeleteLoading}
                    >
                      {bulkDeleteLoading ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Deleting...
                        </>
                      ) : (
                        <>
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete Selected ({selectedProposals.length})
                        </>
                      )}
                    </Button>
                  </PermissionGate>
                </div>
              )}
            </div>

            {proposals.map((proposal) => (
              <Card key={proposal.id} className="border border-gray-200 hover:border-gray-300 transition-colors">
                <CardContent className="p-6">
                  <div className="flex items-start space-x-4">
                    {/* Selection Checkbox */}
                    <div className="pt-1">
                      <Checkbox
                        checked={selectedProposals.includes(proposal.id)}
                        onCheckedChange={(checked) => handleSelectProposal(proposal.id, checked as boolean)}
                      />
                    </div>

                    <div className="flex-1 flex items-start justify-between">
                      <div className="flex-1 space-y-3">
                      {/* Header */}
                      <div className="flex items-center space-x-3">
                        <h3 className="font-semibold text-lg">{proposal.title}</h3>
                        <Badge className={getStatusBadgeColor(proposal.status)}>
                          {proposal.status}
                        </Badge>
                        <Badge variant="outline">
                          {formatVersion(proposal.version)}
                        </Badge>
                      </div>

                      {/* Statistics */}
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div className="flex items-center space-x-2">
                          <FileText className="h-4 w-4 text-blue-500" />
                          <span className="text-gray-600">
                            {proposal.statistics.completedSections}/{proposal.statistics.totalSections} sections
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <BarChart3 className="h-4 w-4 text-green-500" />
                          <span className="text-gray-600">
                            {proposal.statistics.renderedCharts}/{proposal.statistics.totalCharts} charts
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Clock className="h-4 w-4 text-orange-500" />
                          <span className="text-gray-600">
                            {proposal.statistics.completionPercentage}% complete
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <User className="h-4 w-4 text-purple-500" />
                          <span className="text-gray-600">
                            {getUserDisplayName(proposal.createdBy)}
                          </span>
                        </div>
                      </div>

                      {/* Dates */}
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-4 w-4" />
                          <span>Created {formatDateSafe(proposal.createdAt)}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <RefreshCw className="h-4 w-4" />
                          <span>Updated {formatDateSafe(proposal.updatedAt)}</span>
                        </div>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center space-x-2">
                      <PermissionGate
                        resource={PermissionResource.PROPOSALS}
                        action={PermissionAction.READ}
                      >
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => router.push(`/proposals/${proposal.id}`)}
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          View
                        </Button>
                      </PermissionGate>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDownloadProposal(proposal.id)}
                        disabled={downloadingProposals.has(proposal.id)}
                      >
                        {downloadingProposals.has(proposal.id) ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            Downloading...
                          </>
                        ) : (
                          <>
                            <Download className="h-4 w-4 mr-2" />
                            Download
                          </>
                        )}
                      </Button>

                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="outline" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <PermissionGate
                            resource={PermissionResource.PROPOSALS}
                            action={PermissionAction.UPDATE}
                          >
                            <DropdownMenuItem
                              onClick={() => router.push(`/proposals/${proposal.id}/edit`)}
                            >
                              <Edit3 className="h-4 w-4 mr-2" />
                              Edit
                            </DropdownMenuItem>
                          </PermissionGate>

                          <PermissionGate
                            resource={PermissionResource.PROPOSALS}
                            action={PermissionAction.CREATE}
                          >
                            <DropdownMenuItem
                              onClick={() => setDuplicateDialog({
                                open: true,
                                data: {
                                  proposalId: proposal.id,
                                  proposalTitle: proposal.title
                                }
                              })}
                            >
                              <Copy className="h-4 w-4 mr-2" />
                              Duplicate
                            </DropdownMenuItem>
                          </PermissionGate>

                          <DropdownMenuSeparator />

                          <PermissionGate
                            resource={PermissionResource.PROPOSALS}
                            action={PermissionAction.DELETE}
                          >
                            <DropdownMenuItem
                              onClick={() => handleDeleteProposal(proposal.id, proposal.title)}
                              className="text-red-600"
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </PermissionGate>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </CardContent>

      {/* Duplicate Dialog */}
      <Dialog open={duplicateDialog.open} onOpenChange={(open) => setDuplicateDialog({ open, data: null })}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Duplicate Proposal</DialogTitle>
            <DialogDescription>
              Choose how you want to duplicate "{duplicateDialog.data?.proposalTitle}"
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="grid grid-cols-1 gap-3">
              <Button
                variant="outline"
                onClick={() => handleDuplicateProposal('minor')}
                disabled={duplicateLoading}
                className="justify-start h-auto p-4"
              >
                <div className="text-left">
                  <div className="font-medium">New Minor Version</div>
                  <div className="text-sm text-gray-500">
                    Create version 1.1, 1.2, etc. for small updates
                  </div>
                </div>
              </Button>

              <Button
                variant="outline"
                onClick={() => handleDuplicateProposal('major')}
                disabled={duplicateLoading}
                className="justify-start h-auto p-4"
              >
                <div className="text-left">
                  <div className="font-medium">New Major Version</div>
                  <div className="text-sm text-gray-500">
                    Create version 2.0, 3.0, etc. for significant changes
                  </div>
                </div>
              </Button>

              <Button
                variant="outline"
                onClick={() => handleDuplicateProposal('copy')}
                disabled={duplicateLoading}
                className="justify-start h-auto p-4"
              >
                <div className="text-left">
                  <div className="font-medium">Create Copy</div>
                  <div className="text-sm text-gray-500">
                    Create an independent copy with a new version number
                  </div>
                </div>
              </Button>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDuplicateDialog({ open: false, data: null })}
              disabled={duplicateLoading}
            >
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Enhanced Delete Dialog */}
      <DialogComponent />
    </Card>
  );
}
