@echo off
REM Windows batch version of deploy.sh for CRM Platform

setlocal enabledelayedexpansion

REM Configuration
set "ENVIRONMENT=%~1"
if "%ENVIRONMENT%"=="" set "ENVIRONMENT=dev"
if "%ENVIRONMENT%"=="development" set "ENVIRONMENT=dev"

REM Colors for terminal output
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

REM Function to display usage information
:usage
if "%~1"=="show" (
    echo Usage: %0 [environment] [options]
    echo.
    echo Environments:
    echo   dev         Development environment ^(default^)
    echo   staging     Staging environment
    echo   prod        Production environment
    echo.
    echo Options:
    echo   --build       Force rebuild of Docker images
    echo   --no-build    Skip building Docker images
    echo   --pull        Pull latest Docker images
    echo   --no-pull     Skip pulling Docker images
    echo   --migrate     Run database migrations
    echo   --no-migrate  Skip database migrations
    echo   --help        Display this help message
    echo.
    echo Examples:
    echo   %0 dev          Deploy to development environment
    echo   %0 staging      Deploy to staging environment
    echo   %0 prod --pull  Deploy to production with latest images
    exit /b 0
)

REM Parse command line arguments
set "BUILD=true"
set "PULL=false"
set "MIGRATE=true"

REM Parse remaining arguments (skip first argument which is environment)
set "ARG_INDEX=2"
:parse_args
if "%~2"=="" goto :end_parse_args
if "%~2"=="--build" (
    set "BUILD=true"
    shift /2
    goto :parse_args
)
if "%~2"=="--no-build" (
    set "BUILD=false"
    shift /2
    goto :parse_args
)
if "%~2"=="--pull" (
    set "PULL=true"
    shift /2
    goto :parse_args
)
if "%~2"=="--no-pull" (
    set "PULL=false"
    shift /2
    goto :parse_args
)
if "%~2"=="--migrate" (
    set "MIGRATE=true"
    shift /2
    goto :parse_args
)
if "%~2"=="--no-migrate" (
    set "MIGRATE=false"
    shift /2
    goto :parse_args
)
if "%~2"=="--help" (
    goto :usage show
)
if not "%~2"=="" (
    echo %RED%Unknown option: %~2%NC%
    goto :usage show
)
shift /2
goto :parse_args

:end_parse_args

REM Change to project root directory
cd /d "%~dp0..\.."

REM Setup environment-specific configuration
set "COMPOSE_FILE=deployment\docker\docker-compose.yml"
set "COMPOSE_OVERRIDE="
set "ENV_FILE=.env.docker"

if "%ENVIRONMENT%"=="dev" (
    echo %GREEN%Using development environment...%NC%
    set "COMPOSE_FILE=deployment\docker\docker-compose.yml"
    set "COMPOSE_OVERRIDE="
    set "ENV_FILE=.env.docker"
) else if "%ENVIRONMENT%"=="staging" (
    echo %GREEN%Using staging environment...%NC%
    set "COMPOSE_FILE=deployment\docker\docker-compose.yml"
    set "COMPOSE_OVERRIDE=-f deployment\docker\docker-compose.staging.yml"
    set "ENV_FILE=.env.docker"
) else if "%ENVIRONMENT%"=="prod" (
    echo %GREEN%Using production environment...%NC%
    set "COMPOSE_FILE=deployment\docker\docker-compose.yml"
    set "COMPOSE_OVERRIDE=-f deployment\docker\docker-compose.prod.yml"
    set "ENV_FILE=.env.docker"
) else (
    echo %RED%Error: Unknown environment '%ENVIRONMENT%'%NC%
    goto :usage show
)

REM Check if Docker is installed
docker --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo %RED%Error: Docker is not installed or not in PATH%NC%
    exit /b 1
)

REM Check if Docker Compose is installed
docker-compose --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo %RED%Error: Docker Compose is not installed or not in PATH%NC%
    exit /b 1
)

REM Check if environment file exists
if not exist "%ENV_FILE%" (
    echo %RED%Error: Environment file '%ENV_FILE%' not found%NC%
    echo %YELLOW%Create the environment file from the template:%NC%
    echo copy .env.example %ENV_FILE%
    exit /b 1
)

REM Generate SSL certificates for staging and production
if "%ENVIRONMENT%"=="staging" (
    echo %YELLOW%Generating SSL certificates for staging...%NC%
    call scripts\generate-ssl-certs.bat
) else if "%ENVIRONMENT%"=="prod" (
    echo %YELLOW%Generating SSL certificates for production...%NC%
    call scripts\generate-ssl-certs.bat
)

REM Pull latest Docker images if requested
if "%PULL%"=="true" (
    echo %YELLOW%Pulling latest Docker images...%NC%
    docker-compose -f %COMPOSE_FILE% %COMPOSE_OVERRIDE% pull
    echo %GREEN%Docker images pulled successfully.%NC%
)

REM Build Docker images if requested or if this is the first deployment
if "%BUILD%"=="true" (
    echo %YELLOW%Building Docker images...%NC%
    docker-compose -f %COMPOSE_FILE% %COMPOSE_OVERRIDE% build --build-arg BUILDKIT_INLINE_CACHE=1
    echo %GREEN%Docker images built successfully.%NC%
)

REM Start Docker containers
echo %YELLOW%Starting Docker containers...%NC%
docker-compose -f %COMPOSE_FILE% %COMPOSE_OVERRIDE% up -d
echo %GREEN%Docker containers started successfully.%NC%

REM Wait for containers to be healthy
echo %YELLOW%Waiting for containers to be healthy...%NC%
timeout /t 30 /nobreak >nul

REM Run database migrations if requested
if "%MIGRATE%"=="true" (
    echo %YELLOW%Running database migrations...%NC%
    docker-compose -f %COMPOSE_FILE% %COMPOSE_OVERRIDE% exec -T app npx prisma migrate deploy
    if %ERRORLEVEL% equ 0 (
        echo %GREEN%Database migrations completed successfully.%NC%
    ) else (
        echo %YELLOW%Migrations may have already been applied or no migrations needed.%NC%
    )
)

REM Display access URLs
echo.
echo %GREEN%Deployment completed successfully!%NC%
echo.
echo %YELLOW%Access URLs:%NC%
if "%ENVIRONMENT%"=="dev" (
    echo Application: http://localhost:3010
    echo Database (pgAdmin): http://localhost:8082
    echo   - Email: <EMAIL>
    echo   - Password: admin
) else if "%ENVIRONMENT%"=="staging" (
    echo Application: https://staging.crm-platform.com
) else if "%ENVIRONMENT%"=="prod" (
    echo Application: https://crm-platform.com
)
echo.
echo %YELLOW%Container Status:%NC%
docker-compose -f %COMPOSE_FILE% %COMPOSE_OVERRIDE% ps

endlocal
