services:
  # Next.js application
  app:
    build:
      context: ../../
      dockerfile: deployment/docker/Dockerfile
      target: runner
    container_name: crm-app
    restart: unless-stopped
    ports:
      - "3010:3000"
    env_file:
      - ../../.env.docker
    environment:
      # Mermaid PNG generation environment variables
      - PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
      - PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser
      - PUPPETEER_ARGS=--no-sandbox --disable-setuid-sandbox --disable-dev-shm-usage --disable-accelerated-2d-canvas --no-first-run --no-zygote --disable-gpu
      - MERMAID_TEMP_DIR=/tmp/mermaid
      - CHROME_BIN=/usr/bin/chromium-browser
      # Database initialization flags
      - AUTO_MIGRATE=true
      - AUTO_SEED=true
    volumes:
      - ./uploads:/app/uploads
      - mermaid-temp:/tmp/mermaid
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - crm-network
    # Add shared memory size for Chrome
    shm_size: 2gb
    # Security options for Chrome in container
    security_opt:
      - seccomp:unconfined
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s

  # PostgreSQL database
  postgres:
    image: postgres:16-alpine
    container_name: crm-postgres
    restart: unless-stopped
    ports:
      - "5434:5432"
    environment:
      - POSTGRES_USER=admin
      - POSTGRES_PASSWORD=admin
      - POSTGRES_DB=crm_platform
      - POSTGRES_INITDB_ARGS=--auth-host=scram-sha-256
      - POSTGRES_HOST_AUTH_METHOD=scram-sha-256
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./scripts/init-db:/docker-entrypoint-initdb.d:ro
    networks:
      - crm-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U admin -d crm_platform"]
      interval: 10s
      timeout: 5s
      retries: 5
    security_opt:
      - no-new-privileges:true
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 1G

  # Redis for caching and session management
  redis:
    image: redis:7-alpine
    container_name: crm-redis
    restart: unless-stopped
    ports:
      - "6380:6379"
    volumes:
      - redis-data:/data
      - ./config/redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf --appendonly yes
    networks:
      - crm-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    security_opt:
      - no-new-privileges:true
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M

  # pgAdmin for database management (optional)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: crm-pgadmin
    restart: unless-stopped
    ports:
      - "8082:80"
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin
      - PGADMIN_CONFIG_SERVER_MODE=False
      - PGADMIN_CONFIG_MASTER_PASSWORD_REQUIRED=False
    volumes:
      - pgadmin-data:/var/lib/pgadmin
    depends_on:
      - postgres
    networks:
      - crm-network

networks:
  crm-network:
    driver: bridge

volumes:
  postgres-data:
  redis-data:
  mermaid-temp:
  pgadmin-data:
